"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationInputs$ = exports.ConversationInputs$outboundSchema = exports.ConversationInputs$inboundSchema = void 0;
exports.conversationInputsToJSON = conversationInputsToJSON;
exports.conversationInputsFromJSON = conversationInputsFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const inputentries_js_1 = require("./inputentries.js");
/** @internal */
exports.ConversationInputs$inboundSchema = z.union([z.string(), z.array(inputentries_js_1.InputEntries$inboundSchema)]);
/** @internal */
exports.ConversationInputs$outboundSchema = z.union([z.string(), z.array(inputentries_js_1.InputEntries$outboundSchema)]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationInputs$;
(function (ConversationInputs$) {
    /** @deprecated use `ConversationInputs$inboundSchema` instead. */
    ConversationInputs$.inboundSchema = exports.ConversationInputs$inboundSchema;
    /** @deprecated use `ConversationInputs$outboundSchema` instead. */
    ConversationInputs$.outboundSchema = exports.ConversationInputs$outboundSchema;
})(ConversationInputs$ || (exports.ConversationInputs$ = ConversationInputs$ = {}));
function conversationInputsToJSON(conversationInputs) {
    return JSON.stringify(exports.ConversationInputs$outboundSchema.parse(conversationInputs));
}
function conversationInputsFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ConversationInputs$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ConversationInputs' from JSON`);
}
//# sourceMappingURL=conversationinputs.js.map