"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseFormats$ = exports.ResponseFormats$outboundSchema = exports.ResponseFormats$inboundSchema = exports.ResponseFormats = void 0;
const z = __importStar(require("zod"));
/**
 * An object specifying the format that the model must output. Setting to `{ "type": "json_object" }` enables JSON mode, which guarantees the message the model generates is in JSON. When using JSON mode you MUST also instruct the model to produce JSON yourself with a system or a user message.
 */
exports.ResponseFormats = {
    Text: "text",
    JsonObject: "json_object",
    JsonSchema: "json_schema",
};
/** @internal */
exports.ResponseFormats$inboundSchema = z.nativeEnum(exports.ResponseFormats);
/** @internal */
exports.ResponseFormats$outboundSchema = exports.ResponseFormats$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ResponseFormats$;
(function (ResponseFormats$) {
    /** @deprecated use `ResponseFormats$inboundSchema` instead. */
    ResponseFormats$.inboundSchema = exports.ResponseFormats$inboundSchema;
    /** @deprecated use `ResponseFormats$outboundSchema` instead. */
    ResponseFormats$.outboundSchema = exports.ResponseFormats$outboundSchema;
})(ResponseFormats$ || (exports.ResponseFormats$ = ResponseFormats$ = {}));
//# sourceMappingURL=responseformats.js.map