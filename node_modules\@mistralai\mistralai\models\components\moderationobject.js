"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModerationObject$ = exports.ModerationObject$outboundSchema = exports.ModerationObject$inboundSchema = void 0;
exports.moderationObjectToJSON = moderationObjectToJSON;
exports.moderationObjectFromJSON = moderationObjectFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.ModerationObject$inboundSchema = z.object({
    categories: z.record(z.boolean()).optional(),
    category_scores: z.record(z.number()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "category_scores": "categoryScores",
    });
});
/** @internal */
exports.ModerationObject$outboundSchema = z.object({
    categories: z.record(z.boolean()).optional(),
    categoryScores: z.record(z.number()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        categoryScores: "category_scores",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ModerationObject$;
(function (ModerationObject$) {
    /** @deprecated use `ModerationObject$inboundSchema` instead. */
    ModerationObject$.inboundSchema = exports.ModerationObject$inboundSchema;
    /** @deprecated use `ModerationObject$outboundSchema` instead. */
    ModerationObject$.outboundSchema = exports.ModerationObject$outboundSchema;
})(ModerationObject$ || (exports.ModerationObject$ = ModerationObject$ = {}));
function moderationObjectToJSON(moderationObject) {
    return JSON.stringify(exports.ModerationObject$outboundSchema.parse(moderationObject));
}
function moderationObjectFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ModerationObject$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ModerationObject' from JSON`);
}
//# sourceMappingURL=moderationobject.js.map