import * as z from "zod";
import { OpenEnum } from "../../types/enums.js";
export declare const ToolTypes: {
    readonly Function: "function";
};
export type ToolTypes = OpenEnum<typeof ToolTypes>;
/** @internal */
export declare const ToolTypes$inboundSchema: z.ZodType<ToolTypes, z.ZodTypeDef, unknown>;
/** @internal */
export declare const ToolTypes$outboundSchema: z.ZodType<ToolTypes, z.ZodTypeDef, ToolTypes>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace ToolTypes$ {
    /** @deprecated use `ToolTypes$inboundSchema` instead. */
    const inboundSchema: z.ZodType<ToolTypes, z.ZodTypeDef, unknown>;
    /** @deprecated use `ToolTypes$outboundSchema` instead. */
    const outboundSchema: z.ZodType<ToolTypes, z.ZodTypeDef, ToolTypes>;
}
//# sourceMappingURL=tooltypes.d.ts.map