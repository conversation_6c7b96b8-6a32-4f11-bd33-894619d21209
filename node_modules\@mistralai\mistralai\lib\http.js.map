{"version": 3, "file": "http.js", "sourceRoot": "", "sources": ["../src/lib/http.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AA6JH,4CA8CC;AAID,0CAiCC;AAED,sCASC;AAKD,8CAsBC;AAKD,wCAgBC;AAKD,oCAgBC;AAvTD,MAAM,eAAe,GAAY,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;IAC/C,4EAA4E;IAC5E,uEAAuE;IACvE,0EAA0E;IAC1E,yCAAyC;IACzC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACjB,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC;IACtB,CAAC;SAAM,CAAC;QACN,OAAO,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC5B,CAAC;AACH,CAAC,CAAC;AAqBF,MAAa,UAAU;IAMrB,YAAoB,UAA6B,EAAE;QAA/B,YAAO,GAAP,OAAO,CAAwB;QAJ3C,iBAAY,GAAwB,EAAE,CAAC;QACvC,sBAAiB,GAAuB,EAAE,CAAC;QAC3C,kBAAa,GAAmB,EAAE,CAAC;QAGzC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,eAAe,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,OAAgB;QAC5B,IAAI,GAAG,GAAG,OAAO,CAAC;QAClB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACrC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,IAAI,WAAW,EAAE,CAAC;gBAChB,GAAG,GAAG,WAAW,CAAC;YACpB,CAAC;QACH,CAAC;QAED,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAEpC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACtC,MAAM,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACvB,CAAC;YAED,OAAO,GAAG,CAAC;QACb,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC1C,MAAM,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACvB,CAAC;YAED,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;IAkBD,OAAO,CACL,GAAG,IAGqC;QAExC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,eAAe,EAAE,CAAC;YAChC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC;aAAM,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,cAAc,EAAE,CAAC;YACtC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC;aAAM,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE,CAAC;YAClC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,sBAAsB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACnD,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAQD,UAAU,CACR,GAAG,IAGqC;QAExC,IAAI,MAAiB,CAAC;QACtB,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,eAAe,EAAE,CAAC;YAChC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;QAC7B,CAAC;aAAM,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,cAAc,EAAE,CAAC;YACtC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAClC,CAAC;aAAM,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE,CAAC;YAClC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,sBAAsB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC1B,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK;QACH,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3C,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC/C,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QACzD,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAEjD,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AA7GD,gCA6GC;AAID,+EAA+E;AAC/E,mCAAmC;AACnC,MAAM,mBAAmB,GAAG,UAAU,CAAC;AAEvC,SAAgB,gBAAgB,CAAC,QAAkB,EAAE,OAAe;IAClE,4DAA4D;IAC5D,IAAI,OAAO,KAAK,GAAG,EAAE,CAAC;QACpB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,WAAW,GACb,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,IAAI,0BAA0B,CAAC;IAC7E,WAAW,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;IAExC,MAAM,SAAS,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;IAC1E,MAAM,CAAC,QAAQ,GAAG,EAAE,EAAE,GAAG,UAAU,CAAC,GAAG,SAAS,CAAC;IAEjD,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACrC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;IACxD,MAAM,CAAC,OAAO,GAAG,EAAE,EAAE,GAAG,SAAS,CAAC,GAAG,QAAQ,CAAC;IAE9C,MAAM,CAAC,IAAI,GAAG,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACrD,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;QACtB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IACE,QAAQ,KAAK,KAAK;QAClB,OAAO,KAAK,QAAQ;QACpB,GAAG,IAAI,IAAI,KAAK,QAAQ;QACxB,KAAK,OAAO,EAAE,KAAK,QAAQ,EAC3B,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,SAAS,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;QACzC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC;IAClC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;QACnC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3B,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AAEjD,SAAgB,eAAe,CAC7B,QAAkB,EAClB,KAA0B;IAE1B,MAAM,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;IACpC,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAC7D,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QAC1B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE;QAC/B,MAAM,IAAI,GAAG,GAAG,EAAE,EAAE,CAAC;QAErB,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,CAAC;YACjC,OAAO,IAAI,KAAK,MAAM,CAAC;QACzB,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,iCAAiC,MAAM,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,OAAO,YAAY,KAAK,YAAY,CAAC;IACvC,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAgB,aAAa,CAC3B,QAAkB,EAClB,IAAyB,EACzB,kBAA0B;IAE1B,OAAO,CACL,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC;QAC/B,gBAAgB,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAC/C,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,iBAAiB,CAAC,GAAY;IAC5C,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3C,OAAO,KAAK,CAAC;IACf,CAAC;IAED,+BAA+B;IAC/B,MAAM,YAAY,GAChB,GAAG,YAAY,SAAS;QACxB,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;IAE1D,MAAM,SAAS,GACb,GAAG,YAAY,SAAS;QACxB,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;IAEvD,MAAM,QAAQ,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,iBAAiB,CAAC;IAEjE,MAAM,YAAY,GAChB,MAAM,IAAI,GAAG;QACb,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ;QAC5B,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,YAAY,CAAC;IAE1C,OAAO,YAAY,IAAI,SAAS,IAAI,YAAY,IAAI,QAAQ,CAAC;AAC/D,CAAC;AAED;;GAEG;AACH,SAAgB,cAAc,CAAC,GAAY;IACzC,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3C,OAAO,KAAK,CAAC;IACf,CAAC;IAED,uCAAuC;IACvC,MAAM,QAAQ,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,cAAc,CAAC;IAC9D,MAAM,cAAc,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,EAAE,CAAC;IAExD,gCAAgC;IAChC,MAAM,YAAY,GAChB,MAAM,IAAI,GAAG;QACb,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ;QAC5B,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,cAAc,CAAC;IAE5C,OAAO,QAAQ,IAAI,cAAc,IAAI,YAAY,CAAC;AACpD,CAAC;AAED;;GAEG;AACH,SAAgB,YAAY,CAAC,GAAY;IACvC,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3C,OAAO,KAAK,CAAC;IACf,CAAC;IAED,uCAAuC;IACvC,MAAM,QAAQ,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,YAAY,CAAC;IAC5D,MAAM,cAAc,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,EAAE,CAAC;IAExD,gCAAgC;IAChC,MAAM,YAAY,GAChB,MAAM,IAAI,GAAG;QACb,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ;QAC5B,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,cAAc,CAAC;IAE5C,OAAO,QAAQ,IAAI,cAAc,IAAI,YAAY,CAAC;AACpD,CAAC"}