"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentsCompletionRequest$ = exports.AgentsCompletionRequest$outboundSchema = exports.AgentsCompletionRequest$inboundSchema = exports.AgentsCompletionRequestToolChoice$ = exports.AgentsCompletionRequestToolChoice$outboundSchema = exports.AgentsCompletionRequestToolChoice$inboundSchema = exports.AgentsCompletionRequestMessages$ = exports.AgentsCompletionRequestMessages$outboundSchema = exports.AgentsCompletionRequestMessages$inboundSchema = exports.AgentsCompletionRequestStop$ = exports.AgentsCompletionRequestStop$outboundSchema = exports.AgentsCompletionRequestStop$inboundSchema = void 0;
exports.agentsCompletionRequestStopToJSON = agentsCompletionRequestStopToJSON;
exports.agentsCompletionRequestStopFromJSON = agentsCompletionRequestStopFromJSON;
exports.agentsCompletionRequestMessagesToJSON = agentsCompletionRequestMessagesToJSON;
exports.agentsCompletionRequestMessagesFromJSON = agentsCompletionRequestMessagesFromJSON;
exports.agentsCompletionRequestToolChoiceToJSON = agentsCompletionRequestToolChoiceToJSON;
exports.agentsCompletionRequestToolChoiceFromJSON = agentsCompletionRequestToolChoiceFromJSON;
exports.agentsCompletionRequestToJSON = agentsCompletionRequestToJSON;
exports.agentsCompletionRequestFromJSON = agentsCompletionRequestFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const assistantmessage_js_1 = require("./assistantmessage.js");
const mistralpromptmode_js_1 = require("./mistralpromptmode.js");
const prediction_js_1 = require("./prediction.js");
const responseformat_js_1 = require("./responseformat.js");
const systemmessage_js_1 = require("./systemmessage.js");
const tool_js_1 = require("./tool.js");
const toolchoice_js_1 = require("./toolchoice.js");
const toolchoiceenum_js_1 = require("./toolchoiceenum.js");
const toolmessage_js_1 = require("./toolmessage.js");
const usermessage_js_1 = require("./usermessage.js");
/** @internal */
exports.AgentsCompletionRequestStop$inboundSchema = z.union([z.string(), z.array(z.string())]);
/** @internal */
exports.AgentsCompletionRequestStop$outboundSchema = z.union([z.string(), z.array(z.string())]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentsCompletionRequestStop$;
(function (AgentsCompletionRequestStop$) {
    /** @deprecated use `AgentsCompletionRequestStop$inboundSchema` instead. */
    AgentsCompletionRequestStop$.inboundSchema = exports.AgentsCompletionRequestStop$inboundSchema;
    /** @deprecated use `AgentsCompletionRequestStop$outboundSchema` instead. */
    AgentsCompletionRequestStop$.outboundSchema = exports.AgentsCompletionRequestStop$outboundSchema;
})(AgentsCompletionRequestStop$ || (exports.AgentsCompletionRequestStop$ = AgentsCompletionRequestStop$ = {}));
function agentsCompletionRequestStopToJSON(agentsCompletionRequestStop) {
    return JSON.stringify(exports.AgentsCompletionRequestStop$outboundSchema.parse(agentsCompletionRequestStop));
}
function agentsCompletionRequestStopFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.AgentsCompletionRequestStop$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'AgentsCompletionRequestStop' from JSON`);
}
/** @internal */
exports.AgentsCompletionRequestMessages$inboundSchema = z.union([
    systemmessage_js_1.SystemMessage$inboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
        role: v.role,
    }))),
    usermessage_js_1.UserMessage$inboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({ role: v.role }))),
    assistantmessage_js_1.AssistantMessage$inboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
        role: v.role,
    }))),
    toolmessage_js_1.ToolMessage$inboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({ role: v.role }))),
]);
/** @internal */
exports.AgentsCompletionRequestMessages$outboundSchema = z.union([
    systemmessage_js_1.SystemMessage$outboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
        role: v.role,
    }))),
    usermessage_js_1.UserMessage$outboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({ role: v.role }))),
    assistantmessage_js_1.AssistantMessage$outboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
        role: v.role,
    }))),
    toolmessage_js_1.ToolMessage$outboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({ role: v.role }))),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentsCompletionRequestMessages$;
(function (AgentsCompletionRequestMessages$) {
    /** @deprecated use `AgentsCompletionRequestMessages$inboundSchema` instead. */
    AgentsCompletionRequestMessages$.inboundSchema = exports.AgentsCompletionRequestMessages$inboundSchema;
    /** @deprecated use `AgentsCompletionRequestMessages$outboundSchema` instead. */
    AgentsCompletionRequestMessages$.outboundSchema = exports.AgentsCompletionRequestMessages$outboundSchema;
})(AgentsCompletionRequestMessages$ || (exports.AgentsCompletionRequestMessages$ = AgentsCompletionRequestMessages$ = {}));
function agentsCompletionRequestMessagesToJSON(agentsCompletionRequestMessages) {
    return JSON.stringify(exports.AgentsCompletionRequestMessages$outboundSchema.parse(agentsCompletionRequestMessages));
}
function agentsCompletionRequestMessagesFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.AgentsCompletionRequestMessages$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'AgentsCompletionRequestMessages' from JSON`);
}
/** @internal */
exports.AgentsCompletionRequestToolChoice$inboundSchema = z.union([toolchoice_js_1.ToolChoice$inboundSchema, toolchoiceenum_js_1.ToolChoiceEnum$inboundSchema]);
/** @internal */
exports.AgentsCompletionRequestToolChoice$outboundSchema = z.union([toolchoice_js_1.ToolChoice$outboundSchema, toolchoiceenum_js_1.ToolChoiceEnum$outboundSchema]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentsCompletionRequestToolChoice$;
(function (AgentsCompletionRequestToolChoice$) {
    /** @deprecated use `AgentsCompletionRequestToolChoice$inboundSchema` instead. */
    AgentsCompletionRequestToolChoice$.inboundSchema = exports.AgentsCompletionRequestToolChoice$inboundSchema;
    /** @deprecated use `AgentsCompletionRequestToolChoice$outboundSchema` instead. */
    AgentsCompletionRequestToolChoice$.outboundSchema = exports.AgentsCompletionRequestToolChoice$outboundSchema;
})(AgentsCompletionRequestToolChoice$ || (exports.AgentsCompletionRequestToolChoice$ = AgentsCompletionRequestToolChoice$ = {}));
function agentsCompletionRequestToolChoiceToJSON(agentsCompletionRequestToolChoice) {
    return JSON.stringify(exports.AgentsCompletionRequestToolChoice$outboundSchema.parse(agentsCompletionRequestToolChoice));
}
function agentsCompletionRequestToolChoiceFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.AgentsCompletionRequestToolChoice$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'AgentsCompletionRequestToolChoice' from JSON`);
}
/** @internal */
exports.AgentsCompletionRequest$inboundSchema = z.object({
    max_tokens: z.nullable(z.number().int()).optional(),
    stream: z.boolean().default(false),
    stop: z.union([z.string(), z.array(z.string())]).optional(),
    random_seed: z.nullable(z.number().int()).optional(),
    messages: z.array(z.union([
        systemmessage_js_1.SystemMessage$inboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
            role: v.role,
        }))),
        usermessage_js_1.UserMessage$inboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({
            role: v.role,
        }))),
        assistantmessage_js_1.AssistantMessage$inboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
            role: v.role,
        }))),
        toolmessage_js_1.ToolMessage$inboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({
            role: v.role,
        }))),
    ])),
    response_format: responseformat_js_1.ResponseFormat$inboundSchema.optional(),
    tools: z.nullable(z.array(tool_js_1.Tool$inboundSchema)).optional(),
    tool_choice: z.union([toolchoice_js_1.ToolChoice$inboundSchema, toolchoiceenum_js_1.ToolChoiceEnum$inboundSchema])
        .optional(),
    presence_penalty: z.number().optional(),
    frequency_penalty: z.number().optional(),
    n: z.nullable(z.number().int()).optional(),
    prediction: prediction_js_1.Prediction$inboundSchema.optional(),
    parallel_tool_calls: z.boolean().optional(),
    prompt_mode: z.nullable(mistralpromptmode_js_1.MistralPromptMode$inboundSchema).optional(),
    agent_id: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "max_tokens": "maxTokens",
        "random_seed": "randomSeed",
        "response_format": "responseFormat",
        "tool_choice": "toolChoice",
        "presence_penalty": "presencePenalty",
        "frequency_penalty": "frequencyPenalty",
        "parallel_tool_calls": "parallelToolCalls",
        "prompt_mode": "promptMode",
        "agent_id": "agentId",
    });
});
/** @internal */
exports.AgentsCompletionRequest$outboundSchema = z.object({
    maxTokens: z.nullable(z.number().int()).optional(),
    stream: z.boolean().default(false),
    stop: z.union([z.string(), z.array(z.string())]).optional(),
    randomSeed: z.nullable(z.number().int()).optional(),
    messages: z.array(z.union([
        systemmessage_js_1.SystemMessage$outboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
            role: v.role,
        }))),
        usermessage_js_1.UserMessage$outboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({
            role: v.role,
        }))),
        assistantmessage_js_1.AssistantMessage$outboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
            role: v.role,
        }))),
        toolmessage_js_1.ToolMessage$outboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({
            role: v.role,
        }))),
    ])),
    responseFormat: responseformat_js_1.ResponseFormat$outboundSchema.optional(),
    tools: z.nullable(z.array(tool_js_1.Tool$outboundSchema)).optional(),
    toolChoice: z.union([
        toolchoice_js_1.ToolChoice$outboundSchema,
        toolchoiceenum_js_1.ToolChoiceEnum$outboundSchema,
    ]).optional(),
    presencePenalty: z.number().optional(),
    frequencyPenalty: z.number().optional(),
    n: z.nullable(z.number().int()).optional(),
    prediction: prediction_js_1.Prediction$outboundSchema.optional(),
    parallelToolCalls: z.boolean().optional(),
    promptMode: z.nullable(mistralpromptmode_js_1.MistralPromptMode$outboundSchema).optional(),
    agentId: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        maxTokens: "max_tokens",
        randomSeed: "random_seed",
        responseFormat: "response_format",
        toolChoice: "tool_choice",
        presencePenalty: "presence_penalty",
        frequencyPenalty: "frequency_penalty",
        parallelToolCalls: "parallel_tool_calls",
        promptMode: "prompt_mode",
        agentId: "agent_id",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentsCompletionRequest$;
(function (AgentsCompletionRequest$) {
    /** @deprecated use `AgentsCompletionRequest$inboundSchema` instead. */
    AgentsCompletionRequest$.inboundSchema = exports.AgentsCompletionRequest$inboundSchema;
    /** @deprecated use `AgentsCompletionRequest$outboundSchema` instead. */
    AgentsCompletionRequest$.outboundSchema = exports.AgentsCompletionRequest$outboundSchema;
})(AgentsCompletionRequest$ || (exports.AgentsCompletionRequest$ = AgentsCompletionRequest$ = {}));
function agentsCompletionRequestToJSON(agentsCompletionRequest) {
    return JSON.stringify(exports.AgentsCompletionRequest$outboundSchema.parse(agentsCompletionRequest));
}
function agentsCompletionRequestFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.AgentsCompletionRequest$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'AgentsCompletionRequest' from JSON`);
}
//# sourceMappingURL=agentscompletionrequest.js.map