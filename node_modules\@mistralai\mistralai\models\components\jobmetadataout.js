"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobMetadataOut$ = exports.JobMetadataOut$outboundSchema = exports.JobMetadataOut$inboundSchema = void 0;
exports.jobMetadataOutToJSON = jobMetadataOutToJSON;
exports.jobMetadataOutFromJSON = jobMetadataOutFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.JobMetadataOut$inboundSchema = z.object({
    expected_duration_seconds: z.nullable(z.number().int()).optional(),
    cost: z.nullable(z.number()).optional(),
    cost_currency: z.nullable(z.string()).optional(),
    train_tokens_per_step: z.nullable(z.number().int()).optional(),
    train_tokens: z.nullable(z.number().int()).optional(),
    data_tokens: z.nullable(z.number().int()).optional(),
    estimated_start_time: z.nullable(z.number().int()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "expected_duration_seconds": "expectedDurationSeconds",
        "cost_currency": "costCurrency",
        "train_tokens_per_step": "trainTokensPerStep",
        "train_tokens": "trainTokens",
        "data_tokens": "dataTokens",
        "estimated_start_time": "estimatedStartTime",
    });
});
/** @internal */
exports.JobMetadataOut$outboundSchema = z.object({
    expectedDurationSeconds: z.nullable(z.number().int()).optional(),
    cost: z.nullable(z.number()).optional(),
    costCurrency: z.nullable(z.string()).optional(),
    trainTokensPerStep: z.nullable(z.number().int()).optional(),
    trainTokens: z.nullable(z.number().int()).optional(),
    dataTokens: z.nullable(z.number().int()).optional(),
    estimatedStartTime: z.nullable(z.number().int()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        expectedDurationSeconds: "expected_duration_seconds",
        costCurrency: "cost_currency",
        trainTokensPerStep: "train_tokens_per_step",
        trainTokens: "train_tokens",
        dataTokens: "data_tokens",
        estimatedStartTime: "estimated_start_time",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var JobMetadataOut$;
(function (JobMetadataOut$) {
    /** @deprecated use `JobMetadataOut$inboundSchema` instead. */
    JobMetadataOut$.inboundSchema = exports.JobMetadataOut$inboundSchema;
    /** @deprecated use `JobMetadataOut$outboundSchema` instead. */
    JobMetadataOut$.outboundSchema = exports.JobMetadataOut$outboundSchema;
})(JobMetadataOut$ || (exports.JobMetadataOut$ = JobMetadataOut$ = {}));
function jobMetadataOutToJSON(jobMetadataOut) {
    return JSON.stringify(exports.JobMetadataOut$outboundSchema.parse(jobMetadataOut));
}
function jobMetadataOutFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.JobMetadataOut$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'JobMetadataOut' from JSON`);
}
//# sourceMappingURL=jobmetadataout.js.map