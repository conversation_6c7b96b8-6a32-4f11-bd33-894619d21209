"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MistralPromptMode$ = exports.MistralPromptMode$outboundSchema = exports.MistralPromptMode$inboundSchema = exports.MistralPromptMode = void 0;
const z = __importStar(require("zod"));
const enums_js_1 = require("../../types/enums.js");
exports.MistralPromptMode = {
    Reasoning: "reasoning",
};
/** @internal */
exports.MistralPromptMode$inboundSchema = z
    .union([
    z.nativeEnum(exports.MistralPromptMode),
    z.string().transform(enums_js_1.catchUnrecognizedEnum),
]);
/** @internal */
exports.MistralPromptMode$outboundSchema = z.union([
    z.nativeEnum(exports.MistralPromptMode),
    z.string().and(z.custom()),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MistralPromptMode$;
(function (MistralPromptMode$) {
    /** @deprecated use `MistralPromptMode$inboundSchema` instead. */
    MistralPromptMode$.inboundSchema = exports.MistralPromptMode$inboundSchema;
    /** @deprecated use `MistralPromptMode$outboundSchema` instead. */
    MistralPromptMode$.outboundSchema = exports.MistralPromptMode$outboundSchema;
})(MistralPromptMode$ || (exports.MistralPromptMode$ = MistralPromptMode$ = {}));
//# sourceMappingURL=mistralpromptmode.js.map