body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #1a1a2e;
  color: #e0e0e0;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.App {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #1a1a2e;
  color: #e0e0e0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.chat-container {
  background-color: #16213e;
  border-radius: 12px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
  width: 95%;
  max-width: 700px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin: 20px;
}

.chat-header {
  background-color: #0f3460;
  padding: 18px;
  font-size: 1.8em;
  font-weight: bold;
  color: #e0e0e0;
  border-bottom: 1px solid #1a1a2e;
  text-align: center;
}

.messages-container {
  flex-grow: 1;
  padding: 20px;
  overflow-y: auto;
  max-height: 75vh;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.message {
  margin-bottom: 8px;
  padding: 10px 15px;
  border-radius: 18px;
  max-width: 75%;
  word-wrap: break-word;
  line-height: 1.5;
}

.message.user {
  background-color: #007bff;
  color: white;
  align-self: flex-end;
  border-bottom-right-radius: 4px;
}

.message.assistant {
  background-color: #3e4a61;
  color: #e0e0e0;
  align-self: flex-start;
  border-bottom-left-radius: 4px;
}

.input-area {
  display: flex;
  padding: 15px;
  border-top: 1px solid #0f3460;
  background-color: #16213e;
  gap: 10px;
}

.input-area input[type="text"] {
  flex-grow: 1;
  padding: 12px;
  border: 1px solid #0f3460;
  border-radius: 25px;
  background-color: #0f3460;
  color: #e0e0e0;
  font-size: 1em;
  outline: none;
  transition: border-color 0.3s ease;
}

.input-area input[type="text"]:focus {
  border-color: #007bff;
}

.input-area button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1em;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.input-area button:hover {
  background-color: #0056b3;
}

.input-area button:disabled {
  background-color: #555;
  cursor: not-allowed;
}

.provider-select-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  gap: 10px;
  flex-wrap: wrap;
}

.provider-select-container select,
.provider-select-container input[type="text"] {
  padding: 10px;
  border-radius: 8px;
  border: 1px solid #0f3460;
  background-color: #0f3460;
  color: #e0e0e0;
  margin: 0 5px;
  font-size: 0.9em;
  outline: none;
  transition: border-color 0.3s ease;
}

.provider-select-container select:focus,
.provider-select-container input[type="text"]:focus {
  border-color: #007bff;
}

.loading-indicator {
  margin-top: 15px;
  color: #999;
  font-style: italic;
}
