"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OCRUsageInfo$ = exports.OCRUsageInfo$outboundSchema = exports.OCRUsageInfo$inboundSchema = void 0;
exports.ocrUsageInfoToJSON = ocrUsageInfoToJSON;
exports.ocrUsageInfoFromJSON = ocrUsageInfoFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.OCRUsageInfo$inboundSchema = z.object({
    pages_processed: z.number().int(),
    doc_size_bytes: z.nullable(z.number().int()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "pages_processed": "pagesProcessed",
        "doc_size_bytes": "docSizeBytes",
    });
});
/** @internal */
exports.OCRUsageInfo$outboundSchema = z.object({
    pagesProcessed: z.number().int(),
    docSizeBytes: z.nullable(z.number().int()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        pagesProcessed: "pages_processed",
        docSizeBytes: "doc_size_bytes",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var OCRUsageInfo$;
(function (OCRUsageInfo$) {
    /** @deprecated use `OCRUsageInfo$inboundSchema` instead. */
    OCRUsageInfo$.inboundSchema = exports.OCRUsageInfo$inboundSchema;
    /** @deprecated use `OCRUsageInfo$outboundSchema` instead. */
    OCRUsageInfo$.outboundSchema = exports.OCRUsageInfo$outboundSchema;
})(OCRUsageInfo$ || (exports.OCRUsageInfo$ = OCRUsageInfo$ = {}));
function ocrUsageInfoToJSON(ocrUsageInfo) {
    return JSON.stringify(exports.OCRUsageInfo$outboundSchema.parse(ocrUsageInfo));
}
function ocrUsageInfoFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.OCRUsageInfo$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'OCRUsageInfo' from JSON`);
}
//# sourceMappingURL=ocrusageinfo.js.map