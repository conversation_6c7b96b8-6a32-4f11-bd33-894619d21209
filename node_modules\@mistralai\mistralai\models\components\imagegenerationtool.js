"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageGenerationTool$ = exports.ImageGenerationTool$outboundSchema = exports.ImageGenerationTool$inboundSchema = exports.ImageGenerationToolType$ = exports.ImageGenerationToolType$outboundSchema = exports.ImageGenerationToolType$inboundSchema = exports.ImageGenerationToolType = void 0;
exports.imageGenerationToolToJSON = imageGenerationToolToJSON;
exports.imageGenerationToolFromJSON = imageGenerationToolFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
exports.ImageGenerationToolType = {
    ImageGeneration: "image_generation",
};
/** @internal */
exports.ImageGenerationToolType$inboundSchema = z.nativeEnum(exports.ImageGenerationToolType);
/** @internal */
exports.ImageGenerationToolType$outboundSchema = exports.ImageGenerationToolType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ImageGenerationToolType$;
(function (ImageGenerationToolType$) {
    /** @deprecated use `ImageGenerationToolType$inboundSchema` instead. */
    ImageGenerationToolType$.inboundSchema = exports.ImageGenerationToolType$inboundSchema;
    /** @deprecated use `ImageGenerationToolType$outboundSchema` instead. */
    ImageGenerationToolType$.outboundSchema = exports.ImageGenerationToolType$outboundSchema;
})(ImageGenerationToolType$ || (exports.ImageGenerationToolType$ = ImageGenerationToolType$ = {}));
/** @internal */
exports.ImageGenerationTool$inboundSchema = z.object({
    type: exports.ImageGenerationToolType$inboundSchema.default("image_generation"),
});
/** @internal */
exports.ImageGenerationTool$outboundSchema = z.object({
    type: exports.ImageGenerationToolType$outboundSchema.default("image_generation"),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ImageGenerationTool$;
(function (ImageGenerationTool$) {
    /** @deprecated use `ImageGenerationTool$inboundSchema` instead. */
    ImageGenerationTool$.inboundSchema = exports.ImageGenerationTool$inboundSchema;
    /** @deprecated use `ImageGenerationTool$outboundSchema` instead. */
    ImageGenerationTool$.outboundSchema = exports.ImageGenerationTool$outboundSchema;
})(ImageGenerationTool$ || (exports.ImageGenerationTool$ = ImageGenerationTool$ = {}));
function imageGenerationToolToJSON(imageGenerationTool) {
    return JSON.stringify(exports.ImageGenerationTool$outboundSchema.parse(imageGenerationTool));
}
function imageGenerationToolFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ImageGenerationTool$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ImageGenerationTool' from JSON`);
}
//# sourceMappingURL=imagegenerationtool.js.map