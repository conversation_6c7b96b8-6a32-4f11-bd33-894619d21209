"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TrainingFile$ = exports.TrainingFile$outboundSchema = exports.TrainingFile$inboundSchema = void 0;
exports.trainingFileToJSON = trainingFileToJSON;
exports.trainingFileFromJSON = trainingFileFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.TrainingFile$inboundSchema = z.object({
    file_id: z.string(),
    weight: z.number().default(1),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "file_id": "fileId",
    });
});
/** @internal */
exports.TrainingFile$outboundSchema = z.object({
    fileId: z.string(),
    weight: z.number().default(1),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        fileId: "file_id",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var TrainingFile$;
(function (TrainingFile$) {
    /** @deprecated use `TrainingFile$inboundSchema` instead. */
    TrainingFile$.inboundSchema = exports.TrainingFile$inboundSchema;
    /** @deprecated use `TrainingFile$outboundSchema` instead. */
    TrainingFile$.outboundSchema = exports.TrainingFile$outboundSchema;
})(TrainingFile$ || (exports.TrainingFile$ = TrainingFile$ = {}));
function trainingFileToJSON(trainingFile) {
    return JSON.stringify(exports.TrainingFile$outboundSchema.parse(trainingFile));
}
function trainingFileFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.TrainingFile$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'TrainingFile' from JSON`);
}
//# sourceMappingURL=trainingfile.js.map