{"version": 3, "file": "conversationrequest.d.ts", "sourceRoot": "", "sources": ["../../src/models/components/conversationrequest.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC;AAGzB,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAClD,OAAO,EAAE,MAAM,IAAI,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAC9D,OAAO,EAAE,kBAAkB,EAAE,MAAM,iCAAiC,CAAC;AACrE,OAAO,EACL,mBAAmB,EAEnB,4BAA4B,EAE7B,MAAM,0BAA0B,CAAC;AAClC,OAAO,EACL,cAAc,EAEd,uBAAuB,EAExB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EACL,kBAAkB,EAElB,2BAA2B,EAE5B,MAAM,yBAAyB,CAAC;AACjC,OAAO,EACL,mBAAmB,EAEnB,4BAA4B,EAE7B,MAAM,0BAA0B,CAAC;AAClC,OAAO,EACL,YAAY,EAEZ,qBAAqB,EAEtB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EACL,mBAAmB,EAEnB,4BAA4B,EAE7B,MAAM,0BAA0B,CAAC;AAClC,OAAO,EACL,oBAAoB,EAEpB,6BAA6B,EAE9B,MAAM,2BAA2B,CAAC;AACnC,OAAO,EACL,aAAa,EAEb,sBAAsB,EAEvB,MAAM,oBAAoB,CAAC;AAE5B,eAAO,MAAM,gBAAgB;;;CAGnB,CAAC;AACX,MAAM,MAAM,gBAAgB,GAAG,UAAU,CAAC,OAAO,gBAAgB,CAAC,CAAC;AAEnE,MAAM,MAAM,KAAK,GACb,CAAC,mBAAmB,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GACpD,CAAC,mBAAmB,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GACpD,CAAC,aAAa,GAAG;IAAE,IAAI,EAAE,YAAY,CAAA;CAAE,CAAC,GACxC,CAAC,oBAAoB,GAAG;IAAE,IAAI,EAAE,oBAAoB,CAAA;CAAE,CAAC,GACvD,CAAC,mBAAmB,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GACpD,CAAC,YAAY,GAAG;IAAE,IAAI,EAAE,UAAU,CAAA;CAAE,CAAC,CAAC;AAE1C,MAAM,MAAM,mBAAmB,GAAG;IAChC,MAAM,EAAE,kBAAkB,CAAC;IAC3B,MAAM,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC7B,KAAK,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACnC,gBAAgB,CAAC,EAAE,gBAAgB,GAAG,IAAI,GAAG,SAAS,CAAC;IACvD,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACzC,KAAK,CAAC,EACF,KAAK,CACH,CAAC,mBAAmB,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GACpD,CAAC,mBAAmB,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GACpD,CAAC,aAAa,GAAG;QAAE,IAAI,EAAE,YAAY,CAAA;KAAE,CAAC,GACxC,CAAC,oBAAoB,GAAG;QAAE,IAAI,EAAE,oBAAoB,CAAA;KAAE,CAAC,GACvD,CAAC,mBAAmB,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GACpD,CAAC,YAAY,GAAG;QAAE,IAAI,EAAE,UAAU,CAAA;KAAE,CAAC,CACxC,GACC,IAAI,GACJ,SAAS,CAAC;IACd,cAAc,CAAC,EAAE,cAAc,GAAG,IAAI,GAAG,SAAS,CAAC;IACnD,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACxC,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACpC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACnC,CAAC;AAEF,gBAAgB;AAChB,eAAO,MAAM,8BAA8B,EAAE,CAAC,CAAC,aAAa,CAC1D,OAAO,gBAAgB,CACS,CAAC;AAEnC,gBAAgB;AAChB,eAAO,MAAM,+BAA+B,EAAE,CAAC,CAAC,aAAa,CAC3D,OAAO,gBAAgB,CACS,CAAC;AAEnC;;;GAGG;AACH,yBAAiB,iBAAiB,CAAC;IACjC,gEAAgE;IACzD,MAAM,aAAa;;;MAAiC,CAAC;IAC5D,iEAAiE;IAC1D,MAAM,cAAc;;;MAAkC,CAAC;CAC/D;AAED,gBAAgB;AAChB,eAAO,MAAM,mBAAmB,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,UAAU,EAAE,OAAO,CAgCpE,CAAC;AAEL,gBAAgB;AAChB,MAAM,MAAM,cAAc,GACtB,CAAC,4BAA4B,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GAC7D,CAAC,4BAA4B,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GAC7D,CAAC,sBAAsB,GAAG;IAAE,IAAI,EAAE,YAAY,CAAA;CAAE,CAAC,GACjD,CAAC,6BAA6B,GAAG;IAAE,IAAI,EAAE,oBAAoB,CAAA;CAAE,CAAC,GAChE,CAAC,4BAA4B,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GAC7D,CAAC,qBAAqB,GAAG;IAAE,IAAI,EAAE,UAAU,CAAA;CAAE,CAAC,CAAC;AAEnD,gBAAgB;AAChB,eAAO,MAAM,oBAAoB,EAAE,CAAC,CAAC,OAAO,CAC1C,cAAc,EACd,CAAC,CAAC,UAAU,EACZ,KAAK,CAgCL,CAAC;AAEH;;;GAGG;AACH,yBAAiB,MAAM,CAAC;IACtB,qDAAqD;IAC9C,MAAM,aAAa,yCAAsB,CAAC;IACjD,sDAAsD;IAC/C,MAAM,cAAc,gDAAuB,CAAC;IACnD,gDAAgD;IAChD,KAAY,QAAQ,GAAG,cAAc,CAAC;CACvC;AAED,wBAAgB,WAAW,CAAC,KAAK,EAAE,KAAK,GAAG,MAAM,CAEhD;AAED,wBAAgB,aAAa,CAC3B,UAAU,EAAE,MAAM,GACjB,eAAe,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAM5C;AAED,gBAAgB;AAChB,eAAO,MAAM,iCAAiC,EAAE,CAAC,CAAC,OAAO,CACvD,mBAAmB,EACnB,CAAC,CAAC,UAAU,EACZ,OAAO,CAsDP,CAAC;AAEH,gBAAgB;AAChB,MAAM,MAAM,4BAA4B,GAAG;IACzC,MAAM,EAAE,2BAA2B,CAAC;IACpC,MAAM,EAAE,OAAO,CAAC;IAChB,KAAK,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACnC,iBAAiB,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAC9C,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACzC,KAAK,CAAC,EACF,KAAK,CACH,CAAC,4BAA4B,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GAC7D,CAAC,4BAA4B,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GAC7D,CAAC,sBAAsB,GAAG;QAAE,IAAI,EAAE,YAAY,CAAA;KAAE,CAAC,GACjD,CAAC,6BAA6B,GAAG;QAAE,IAAI,EAAE,oBAAoB,CAAA;KAAE,CAAC,GAChE,CAAC,4BAA4B,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GAC7D,CAAC,qBAAqB,GAAG;QAAE,IAAI,EAAE,UAAU,CAAA;KAAE,CAAC,CACjD,GACC,IAAI,GACJ,SAAS,CAAC;IACd,eAAe,CAAC,EAAE,uBAAuB,GAAG,IAAI,GAAG,SAAS,CAAC;IAC7D,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACxC,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACrC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACnC,CAAC;AAEF,gBAAgB;AAChB,eAAO,MAAM,kCAAkC,EAAE,CAAC,CAAC,OAAO,CACxD,4BAA4B,EAC5B,CAAC,CAAC,UAAU,EACZ,mBAAmB,CAsDnB,CAAC;AAEH;;;GAGG;AACH,yBAAiB,oBAAoB,CAAC;IACpC,mEAAmE;IAC5D,MAAM,aAAa,uDAAoC,CAAC;IAC/D,oEAAoE;IAC7D,MAAM,cAAc,4EAAqC,CAAC;IACjE,8DAA8D;IAC9D,KAAY,QAAQ,GAAG,4BAA4B,CAAC;CACrD;AAED,wBAAgB,yBAAyB,CACvC,mBAAmB,EAAE,mBAAmB,GACvC,MAAM,CAIR;AAED,wBAAgB,2BAA2B,CACzC,UAAU,EAAE,MAAM,GACjB,eAAe,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,CAM1D"}