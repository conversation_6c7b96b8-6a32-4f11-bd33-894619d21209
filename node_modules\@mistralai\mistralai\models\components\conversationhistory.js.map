{"version": 3, "file": "conversationhistory.js", "sourceRoot": "", "sources": ["../../src/models/components/conversationhistory.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;AAgJH,sCAEC;AAED,0CAQC;AAiFD,8DAMC;AAED,kEAQC;AA3PD,uCAAyB;AACzB,2DAA0D;AAC1D,qDAAiD;AAIjD,iEAKgC;AAChC,iEAKgC;AAChC,qEAKkC;AAClC,iEAKgC;AAChC,mEAKiC;AACjC,mEAKiC;AAEpB,QAAA,yBAAyB,GAAG;IACvC,mBAAmB,EAAE,sBAAsB;CACnC,CAAC;AA6BX,gBAAgB;AACH,QAAA,uCAAuC,GAEhD,CAAC,CAAC,UAAU,CAAC,iCAAyB,CAAC,CAAC;AAE5C,gBAAgB;AACH,QAAA,wCAAwC,GAEjD,+CAAuC,CAAC;AAE5C;;;GAGG;AACH,IAAiB,0BAA0B,CAK1C;AALD,WAAiB,0BAA0B;IACzC,yEAAyE;IAC5D,wCAAa,GAAG,+CAAuC,CAAC;IACrE,0EAA0E;IAC7D,yCAAc,GAAG,gDAAwC,CAAC;AACzE,CAAC,EALgB,0BAA0B,0CAA1B,0BAA0B,QAK1C;AAED,gBAAgB;AACH,QAAA,qBAAqB,GAChC,CAAC,CAAC,KAAK,CAAC;IACN,sDAA+B;IAC/B,0DAAiC;IACjC,wDAAgC;IAChC,sDAA+B;IAC/B,wDAAgC;IAChC,sDAA+B;CAChC,CAAC,CAAC;AAWL,gBAAgB;AACH,QAAA,sBAAsB,GAI/B,CAAC,CAAC,KAAK,CAAC;IACV,uDAAgC;IAChC,2DAAkC;IAClC,yDAAiC;IACjC,uDAAgC;IAChC,yDAAiC;IACjC,uDAAgC;CACjC,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,QAAQ,CAOxB;AAPD,WAAiB,QAAQ;IACvB,uDAAuD;IAC1C,sBAAa,GAAG,6BAAqB,CAAC;IACnD,wDAAwD;IAC3C,uBAAc,GAAG,8BAAsB,CAAC;AAGvD,CAAC,EAPgB,QAAQ,wBAAR,QAAQ,QAOxB;AAED,SAAgB,aAAa,CAAC,OAAgB;IAC5C,OAAO,IAAI,CAAC,SAAS,CAAC,8BAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AAC/D,CAAC;AAED,SAAgB,eAAe,CAC7B,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,6BAAqB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACjD,qCAAqC,CACtC,CAAC;AACJ,CAAC;AAED,gBAAgB;AACH,QAAA,iCAAiC,GAI1C,CAAC,CAAC,MAAM,CAAC;IACX,MAAM,EAAE,+CAAuC,CAAC,OAAO,CACrD,sBAAsB,CACvB;IACD,eAAe,EAAE,CAAC,CAAC,MAAM,EAAE;IAC3B,OAAO,EAAE,CAAC,CAAC,KAAK,CACd,CAAC,CAAC,KAAK,CAAC;QACN,sDAA+B;QAC/B,0DAAiC;QACjC,wDAAgC;QAChC,sDAA+B;QAC/B,wDAAgC;QAChC,sDAA+B;KAChC,CAAC,CACH;CACF,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,iBAAiB,EAAE,gBAAgB;KACpC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAgBH,gBAAgB;AACH,QAAA,kCAAkC,GAI3C,CAAC,CAAC,MAAM,CAAC;IACX,MAAM,EAAE,gDAAwC,CAAC,OAAO,CACtD,sBAAsB,CACvB;IACD,cAAc,EAAE,CAAC,CAAC,MAAM,EAAE;IAC1B,OAAO,EAAE,CAAC,CAAC,KAAK,CACd,CAAC,CAAC,KAAK,CAAC;QACN,uDAAgC;QAChC,2DAAkC;QAClC,yDAAiC;QACjC,uDAAgC;QAChC,yDAAiC;QACjC,uDAAgC;KACjC,CAAC,CACH;CACF,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,cAAc,EAAE,iBAAiB;KAClC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,oBAAoB,CAOpC;AAPD,WAAiB,oBAAoB;IACnC,mEAAmE;IACtD,kCAAa,GAAG,yCAAiC,CAAC;IAC/D,oEAAoE;IACvD,mCAAc,GAAG,0CAAkC,CAAC;AAGnE,CAAC,EAPgB,oBAAoB,oCAApB,oBAAoB,QAOpC;AAED,SAAgB,yBAAyB,CACvC,mBAAwC;IAExC,OAAO,IAAI,CAAC,SAAS,CACnB,0CAAkC,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAC9D,CAAC;AACJ,CAAC;AAED,SAAgB,2BAA2B,CACzC,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,yCAAiC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC7D,iDAAiD,CAClD,CAAC;AACJ,CAAC"}