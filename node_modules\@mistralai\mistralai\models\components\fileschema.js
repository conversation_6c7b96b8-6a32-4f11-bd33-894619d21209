"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileSchema$ = exports.FileSchema$outboundSchema = exports.FileSchema$inboundSchema = void 0;
exports.fileSchemaToJSON = fileSchemaToJSON;
exports.fileSchemaFromJSON = fileSchemaFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const filepurpose_js_1 = require("./filepurpose.js");
const sampletype_js_1 = require("./sampletype.js");
const source_js_1 = require("./source.js");
/** @internal */
exports.FileSchema$inboundSchema = z.object({
    id: z.string(),
    object: z.string(),
    bytes: z.number().int(),
    created_at: z.number().int(),
    filename: z.string(),
    purpose: filepurpose_js_1.FilePurpose$inboundSchema,
    sample_type: sampletype_js_1.SampleType$inboundSchema,
    num_lines: z.nullable(z.number().int()).optional(),
    source: source_js_1.Source$inboundSchema,
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "bytes": "sizeBytes",
        "created_at": "createdAt",
        "sample_type": "sampleType",
        "num_lines": "numLines",
    });
});
/** @internal */
exports.FileSchema$outboundSchema = z.object({
    id: z.string(),
    object: z.string(),
    sizeBytes: z.number().int(),
    createdAt: z.number().int(),
    filename: z.string(),
    purpose: filepurpose_js_1.FilePurpose$outboundSchema,
    sampleType: sampletype_js_1.SampleType$outboundSchema,
    numLines: z.nullable(z.number().int()).optional(),
    source: source_js_1.Source$outboundSchema,
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        sizeBytes: "bytes",
        createdAt: "created_at",
        sampleType: "sample_type",
        numLines: "num_lines",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FileSchema$;
(function (FileSchema$) {
    /** @deprecated use `FileSchema$inboundSchema` instead. */
    FileSchema$.inboundSchema = exports.FileSchema$inboundSchema;
    /** @deprecated use `FileSchema$outboundSchema` instead. */
    FileSchema$.outboundSchema = exports.FileSchema$outboundSchema;
})(FileSchema$ || (exports.FileSchema$ = FileSchema$ = {}));
function fileSchemaToJSON(fileSchema) {
    return JSON.stringify(exports.FileSchema$outboundSchema.parse(fileSchema));
}
function fileSchemaFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.FileSchema$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'FileSchema' from JSON`);
}
//# sourceMappingURL=fileschema.js.map