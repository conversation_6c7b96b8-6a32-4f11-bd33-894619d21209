"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeInterpreterTool$ = exports.CodeInterpreterTool$outboundSchema = exports.CodeInterpreterTool$inboundSchema = exports.CodeInterpreterToolType$ = exports.CodeInterpreterToolType$outboundSchema = exports.CodeInterpreterToolType$inboundSchema = exports.CodeInterpreterToolType = void 0;
exports.codeInterpreterToolToJSON = codeInterpreterToolToJSON;
exports.codeInterpreterToolFromJSON = codeInterpreterToolFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
exports.CodeInterpreterToolType = {
    CodeInterpreter: "code_interpreter",
};
/** @internal */
exports.CodeInterpreterToolType$inboundSchema = z.nativeEnum(exports.CodeInterpreterToolType);
/** @internal */
exports.CodeInterpreterToolType$outboundSchema = exports.CodeInterpreterToolType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var CodeInterpreterToolType$;
(function (CodeInterpreterToolType$) {
    /** @deprecated use `CodeInterpreterToolType$inboundSchema` instead. */
    CodeInterpreterToolType$.inboundSchema = exports.CodeInterpreterToolType$inboundSchema;
    /** @deprecated use `CodeInterpreterToolType$outboundSchema` instead. */
    CodeInterpreterToolType$.outboundSchema = exports.CodeInterpreterToolType$outboundSchema;
})(CodeInterpreterToolType$ || (exports.CodeInterpreterToolType$ = CodeInterpreterToolType$ = {}));
/** @internal */
exports.CodeInterpreterTool$inboundSchema = z.object({
    type: exports.CodeInterpreterToolType$inboundSchema.default("code_interpreter"),
});
/** @internal */
exports.CodeInterpreterTool$outboundSchema = z.object({
    type: exports.CodeInterpreterToolType$outboundSchema.default("code_interpreter"),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var CodeInterpreterTool$;
(function (CodeInterpreterTool$) {
    /** @deprecated use `CodeInterpreterTool$inboundSchema` instead. */
    CodeInterpreterTool$.inboundSchema = exports.CodeInterpreterTool$inboundSchema;
    /** @deprecated use `CodeInterpreterTool$outboundSchema` instead. */
    CodeInterpreterTool$.outboundSchema = exports.CodeInterpreterTool$outboundSchema;
})(CodeInterpreterTool$ || (exports.CodeInterpreterTool$ = CodeInterpreterTool$ = {}));
function codeInterpreterToolToJSON(codeInterpreterTool) {
    return JSON.stringify(exports.CodeInterpreterTool$outboundSchema.parse(codeInterpreterTool));
}
function codeInterpreterToolFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.CodeInterpreterTool$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'CodeInterpreterTool' from JSON`);
}
//# sourceMappingURL=codeinterpretertool.js.map