{"version": 3, "file": "deltamessage.d.ts", "sourceRoot": "", "sources": ["../../src/models/components/deltamessage.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC;AAGzB,OAAO,EAAE,MAAM,IAAI,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAC9D,OAAO,EAAE,kBAAkB,EAAE,MAAM,iCAAiC,CAAC;AACrE,OAAO,EACL,YAAY,EAEZ,qBAAqB,EAEtB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EACL,QAAQ,EAER,iBAAiB,EAElB,MAAM,eAAe,CAAC;AAEvB,MAAM,MAAM,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;AAEnD,MAAM,MAAM,YAAY,GAAG;IACzB,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC,OAAO,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,IAAI,GAAG,SAAS,CAAC;IAC1D,SAAS,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI,GAAG,SAAS,CAAC;CAChD,CAAC;AAEF,gBAAgB;AAChB,eAAO,MAAM,qBAAqB,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,UAAU,EAAE,OAAO,CAChB,CAAC;AAE7D,gBAAgB;AAChB,MAAM,MAAM,gBAAgB,GAAG,MAAM,GAAG,KAAK,CAAC,qBAAqB,CAAC,CAAC;AAErE,gBAAgB;AAChB,eAAO,MAAM,sBAAsB,EAAE,CAAC,CAAC,OAAO,CAC5C,gBAAgB,EAChB,CAAC,CAAC,UAAU,EACZ,OAAO,CACsD,CAAC;AAEhE;;;GAGG;AACH,yBAAiB,QAAQ,CAAC;IACxB,uDAAuD;IAChD,MAAM,aAAa,2CAAwB,CAAC;IACnD,wDAAwD;IACjD,MAAM,cAAc,oDAAyB,CAAC;IACrD,kDAAkD;IAClD,KAAY,QAAQ,GAAG,gBAAgB,CAAC;CACzC;AAED,wBAAgB,aAAa,CAAC,OAAO,EAAE,OAAO,GAAG,MAAM,CAEtD;AAED,wBAAgB,eAAe,CAC7B,UAAU,EAAE,MAAM,GACjB,eAAe,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAM9C;AAED,gBAAgB;AAChB,eAAO,MAAM,0BAA0B,EAAE,CAAC,CAAC,OAAO,CAChD,YAAY,EACZ,CAAC,CAAC,UAAU,EACZ,OAAO,CAWP,CAAC;AAEH,gBAAgB;AAChB,MAAM,MAAM,qBAAqB,GAAG;IAClC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC,OAAO,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,qBAAqB,CAAC,GAAG,IAAI,GAAG,SAAS,CAAC;IACnE,UAAU,CAAC,EAAE,KAAK,CAAC,iBAAiB,CAAC,GAAG,IAAI,GAAG,SAAS,CAAC;CAC1D,CAAC;AAEF,gBAAgB;AAChB,eAAO,MAAM,2BAA2B,EAAE,CAAC,CAAC,OAAO,CACjD,qBAAqB,EACrB,CAAC,CAAC,UAAU,EACZ,YAAY,CAWZ,CAAC;AAEH;;;GAGG;AACH,yBAAiB,aAAa,CAAC;IAC7B,4DAA4D;IACrD,MAAM,aAAa,gDAA6B,CAAC;IACxD,6DAA6D;IACtD,MAAM,cAAc,8DAA8B,CAAC;IAC1D,uDAAuD;IACvD,KAAY,QAAQ,GAAG,qBAAqB,CAAC;CAC9C;AAED,wBAAgB,kBAAkB,CAAC,YAAY,EAAE,YAAY,GAAG,MAAM,CAErE;AAED,wBAAgB,oBAAoB,CAClC,UAAU,EAAE,MAAM,GACjB,eAAe,CAAC,YAAY,EAAE,kBAAkB,CAAC,CAMnD"}