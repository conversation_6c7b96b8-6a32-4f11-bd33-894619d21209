"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WandbIntegrationOut$ = exports.WandbIntegrationOut$outboundSchema = exports.WandbIntegrationOut$inboundSchema = exports.WandbIntegrationOutType$ = exports.WandbIntegrationOutType$outboundSchema = exports.WandbIntegrationOutType$inboundSchema = exports.WandbIntegrationOutType = void 0;
exports.wandbIntegrationOutToJSON = wandbIntegrationOutToJSON;
exports.wandbIntegrationOutFromJSON = wandbIntegrationOutFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
exports.WandbIntegrationOutType = {
    Wandb: "wandb",
};
/** @internal */
exports.WandbIntegrationOutType$inboundSchema = z.nativeEnum(exports.WandbIntegrationOutType);
/** @internal */
exports.WandbIntegrationOutType$outboundSchema = exports.WandbIntegrationOutType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WandbIntegrationOutType$;
(function (WandbIntegrationOutType$) {
    /** @deprecated use `WandbIntegrationOutType$inboundSchema` instead. */
    WandbIntegrationOutType$.inboundSchema = exports.WandbIntegrationOutType$inboundSchema;
    /** @deprecated use `WandbIntegrationOutType$outboundSchema` instead. */
    WandbIntegrationOutType$.outboundSchema = exports.WandbIntegrationOutType$outboundSchema;
})(WandbIntegrationOutType$ || (exports.WandbIntegrationOutType$ = WandbIntegrationOutType$ = {}));
/** @internal */
exports.WandbIntegrationOut$inboundSchema = z.object({
    type: exports.WandbIntegrationOutType$inboundSchema.default("wandb"),
    project: z.string(),
    name: z.nullable(z.string()).optional(),
    run_name: z.nullable(z.string()).optional(),
    url: z.nullable(z.string()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "run_name": "runName",
    });
});
/** @internal */
exports.WandbIntegrationOut$outboundSchema = z.object({
    type: exports.WandbIntegrationOutType$outboundSchema.default("wandb"),
    project: z.string(),
    name: z.nullable(z.string()).optional(),
    runName: z.nullable(z.string()).optional(),
    url: z.nullable(z.string()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        runName: "run_name",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WandbIntegrationOut$;
(function (WandbIntegrationOut$) {
    /** @deprecated use `WandbIntegrationOut$inboundSchema` instead. */
    WandbIntegrationOut$.inboundSchema = exports.WandbIntegrationOut$inboundSchema;
    /** @deprecated use `WandbIntegrationOut$outboundSchema` instead. */
    WandbIntegrationOut$.outboundSchema = exports.WandbIntegrationOut$outboundSchema;
})(WandbIntegrationOut$ || (exports.WandbIntegrationOut$ = WandbIntegrationOut$ = {}));
function wandbIntegrationOutToJSON(wandbIntegrationOut) {
    return JSON.stringify(exports.WandbIntegrationOut$outboundSchema.parse(wandbIntegrationOut));
}
function wandbIntegrationOutFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.WandbIntegrationOut$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'WandbIntegrationOut' from JSON`);
}
//# sourceMappingURL=wandbintegrationout.js.map