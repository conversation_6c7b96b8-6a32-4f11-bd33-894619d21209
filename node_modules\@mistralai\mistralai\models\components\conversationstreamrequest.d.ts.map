{"version": 3, "file": "conversationstreamrequest.d.ts", "sourceRoot": "", "sources": ["../../src/models/components/conversationstreamrequest.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC;AAGzB,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAClD,OAAO,EAAE,MAAM,IAAI,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAC9D,OAAO,EAAE,kBAAkB,EAAE,MAAM,iCAAiC,CAAC;AACrE,OAAO,EACL,mBAAmB,EAEnB,4BAA4B,EAE7B,MAAM,0BAA0B,CAAC;AAClC,OAAO,EACL,cAAc,EAEd,uBAAuB,EAExB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EACL,kBAAkB,EAElB,2BAA2B,EAE5B,MAAM,yBAAyB,CAAC;AACjC,OAAO,EACL,mBAAmB,EAEnB,4BAA4B,EAE7B,MAAM,0BAA0B,CAAC;AAClC,OAAO,EACL,YAAY,EAEZ,qBAAqB,EAEtB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EACL,mBAAmB,EAEnB,4BAA4B,EAE7B,MAAM,0BAA0B,CAAC;AAClC,OAAO,EACL,oBAAoB,EAEpB,6BAA6B,EAE9B,MAAM,2BAA2B,CAAC;AACnC,OAAO,EACL,aAAa,EAEb,sBAAsB,EAEvB,MAAM,oBAAoB,CAAC;AAE5B,eAAO,MAAM,yCAAyC;;;CAG5C,CAAC;AACX,MAAM,MAAM,yCAAyC,GAAG,UAAU,CAChE,OAAO,yCAAyC,CACjD,CAAC;AAEF,MAAM,MAAM,8BAA8B,GACtC,CAAC,mBAAmB,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GACpD,CAAC,mBAAmB,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GACpD,CAAC,aAAa,GAAG;IAAE,IAAI,EAAE,YAAY,CAAA;CAAE,CAAC,GACxC,CAAC,oBAAoB,GAAG;IAAE,IAAI,EAAE,oBAAoB,CAAA;CAAE,CAAC,GACvD,CAAC,mBAAmB,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GACpD,CAAC,YAAY,GAAG;IAAE,IAAI,EAAE,UAAU,CAAA;CAAE,CAAC,CAAC;AAE1C,MAAM,MAAM,yBAAyB,GAAG;IACtC,MAAM,EAAE,kBAAkB,CAAC;IAC3B,MAAM,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC7B,KAAK,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACnC,gBAAgB,CAAC,EACb,yCAAyC,GACzC,IAAI,GACJ,SAAS,CAAC;IACd,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACzC,KAAK,CAAC,EACF,KAAK,CACH,CAAC,mBAAmB,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GACpD,CAAC,mBAAmB,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GACpD,CAAC,aAAa,GAAG;QAAE,IAAI,EAAE,YAAY,CAAA;KAAE,CAAC,GACxC,CAAC,oBAAoB,GAAG;QAAE,IAAI,EAAE,oBAAoB,CAAA;KAAE,CAAC,GACvD,CAAC,mBAAmB,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GACpD,CAAC,YAAY,GAAG;QAAE,IAAI,EAAE,UAAU,CAAA;KAAE,CAAC,CACxC,GACC,IAAI,GACJ,SAAS,CAAC;IACd,cAAc,CAAC,EAAE,cAAc,GAAG,IAAI,GAAG,SAAS,CAAC;IACnD,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACxC,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACpC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACnC,CAAC;AAEF,gBAAgB;AAChB,eAAO,MAAM,uDAAuD,EAClE,CAAC,CAAC,aAAa,CAAC,OAAO,yCAAyC,CACR,CAAC;AAE3D,gBAAgB;AAChB,eAAO,MAAM,wDAAwD,EACnE,CAAC,CAAC,aAAa,CAAC,OAAO,yCAAyC,CACP,CAAC;AAE5D;;;GAGG;AACH,yBAAiB,0CAA0C,CAAC;IAC1D,yFAAyF;IAClF,MAAM,aAAa;;;MAC+B,CAAC;IAC1D,0FAA0F;IACnF,MAAM,cAAc;;;MAC+B,CAAC;CAC5D;AAED,gBAAgB;AAChB,eAAO,MAAM,4CAA4C,EAAE,CAAC,CAAC,OAAO,CAClE,8BAA8B,EAC9B,CAAC,CAAC,UAAU,EACZ,OAAO,CAgCP,CAAC;AAEH,gBAAgB;AAChB,MAAM,MAAM,uCAAuC,GAC/C,CAAC,4BAA4B,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GAC7D,CAAC,4BAA4B,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GAC7D,CAAC,sBAAsB,GAAG;IAAE,IAAI,EAAE,YAAY,CAAA;CAAE,CAAC,GACjD,CAAC,6BAA6B,GAAG;IAAE,IAAI,EAAE,oBAAoB,CAAA;CAAE,CAAC,GAChE,CAAC,4BAA4B,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GAC7D,CAAC,qBAAqB,GAAG;IAAE,IAAI,EAAE,UAAU,CAAA;CAAE,CAAC,CAAC;AAEnD,gBAAgB;AAChB,eAAO,MAAM,6CAA6C,EAAE,CAAC,CAAC,OAAO,CACnE,uCAAuC,EACvC,CAAC,CAAC,UAAU,EACZ,8BAA8B,CAgC9B,CAAC;AAEH;;;GAGG;AACH,yBAAiB,+BAA+B,CAAC;IAC/C,8EAA8E;IACvE,MAAM,aAAa,kEAA+C,CAAC;IAC1E,+EAA+E;IACxE,MAAM,cAAc,kGAAgD,CAAC;IAC5E,yEAAyE;IACzE,KAAY,QAAQ,GAAG,uCAAuC,CAAC;CAChE;AAED,wBAAgB,oCAAoC,CAClD,8BAA8B,EAAE,8BAA8B,GAC7D,MAAM,CAMR;AAED,wBAAgB,sCAAsC,CACpD,UAAU,EAAE,MAAM,GACjB,eAAe,CAAC,8BAA8B,EAAE,kBAAkB,CAAC,CAMrE;AAED,gBAAgB;AAChB,eAAO,MAAM,uCAAuC,EAAE,CAAC,CAAC,OAAO,CAC7D,yBAAyB,EACzB,CAAC,CAAC,UAAU,EACZ,OAAO,CAwDP,CAAC;AAEH,gBAAgB;AAChB,MAAM,MAAM,kCAAkC,GAAG;IAC/C,MAAM,EAAE,2BAA2B,CAAC;IACpC,MAAM,EAAE,OAAO,CAAC;IAChB,KAAK,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACnC,iBAAiB,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAC9C,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACzC,KAAK,CAAC,EACF,KAAK,CACH,CAAC,4BAA4B,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GAC7D,CAAC,4BAA4B,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GAC7D,CAAC,sBAAsB,GAAG;QAAE,IAAI,EAAE,YAAY,CAAA;KAAE,CAAC,GACjD,CAAC,6BAA6B,GAAG;QAAE,IAAI,EAAE,oBAAoB,CAAA;KAAE,CAAC,GAChE,CAAC,4BAA4B,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GAC7D,CAAC,qBAAqB,GAAG;QAAE,IAAI,EAAE,UAAU,CAAA;KAAE,CAAC,CACjD,GACC,IAAI,GACJ,SAAS,CAAC;IACd,eAAe,CAAC,EAAE,uBAAuB,GAAG,IAAI,GAAG,SAAS,CAAC;IAC7D,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACxC,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACrC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACnC,CAAC;AAEF,gBAAgB;AAChB,eAAO,MAAM,wCAAwC,EAAE,CAAC,CAAC,OAAO,CAC9D,kCAAkC,EAClC,CAAC,CAAC,UAAU,EACZ,yBAAyB,CAwDzB,CAAC;AAEH;;;GAGG;AACH,yBAAiB,0BAA0B,CAAC;IAC1C,yEAAyE;IAClE,MAAM,aAAa,6DAA0C,CAAC;IACrE,0EAA0E;IACnE,MAAM,cAAc,wFAA2C,CAAC;IACvE,oEAAoE;IACpE,KAAY,QAAQ,GAAG,kCAAkC,CAAC;CAC3D;AAED,wBAAgB,+BAA+B,CAC7C,yBAAyB,EAAE,yBAAyB,GACnD,MAAM,CAIR;AAED,wBAAgB,iCAAiC,CAC/C,UAAU,EAAE,MAAM,GACjB,eAAe,CAAC,yBAAyB,EAAE,kBAAkB,CAAC,CAMhE"}