"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmbeddingResponse$ = exports.EmbeddingResponse$outboundSchema = exports.EmbeddingResponse$inboundSchema = void 0;
exports.embeddingResponseToJSON = embeddingResponseToJSON;
exports.embeddingResponseFromJSON = embeddingResponseFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const embeddingresponsedata_js_1 = require("./embeddingresponsedata.js");
const usageinfo_js_1 = require("./usageinfo.js");
/** @internal */
exports.EmbeddingResponse$inboundSchema = z.object({
    id: z.string(),
    object: z.string(),
    model: z.string(),
    usage: usageinfo_js_1.UsageInfo$inboundSchema,
    data: z.array(embeddingresponsedata_js_1.EmbeddingResponseData$inboundSchema),
});
/** @internal */
exports.EmbeddingResponse$outboundSchema = z.object({
    id: z.string(),
    object: z.string(),
    model: z.string(),
    usage: usageinfo_js_1.UsageInfo$outboundSchema,
    data: z.array(embeddingresponsedata_js_1.EmbeddingResponseData$outboundSchema),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var EmbeddingResponse$;
(function (EmbeddingResponse$) {
    /** @deprecated use `EmbeddingResponse$inboundSchema` instead. */
    EmbeddingResponse$.inboundSchema = exports.EmbeddingResponse$inboundSchema;
    /** @deprecated use `EmbeddingResponse$outboundSchema` instead. */
    EmbeddingResponse$.outboundSchema = exports.EmbeddingResponse$outboundSchema;
})(EmbeddingResponse$ || (exports.EmbeddingResponse$ = EmbeddingResponse$ = {}));
function embeddingResponseToJSON(embeddingResponse) {
    return JSON.stringify(exports.EmbeddingResponse$outboundSchema.parse(embeddingResponse));
}
function embeddingResponseFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.EmbeddingResponse$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'EmbeddingResponse' from JSON`);
}
//# sourceMappingURL=embeddingresponse.js.map