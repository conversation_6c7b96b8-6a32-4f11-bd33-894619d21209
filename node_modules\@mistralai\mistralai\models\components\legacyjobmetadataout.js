"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegacyJobMetadataOut$ = exports.LegacyJobMetadataOut$outboundSchema = exports.LegacyJobMetadataOut$inboundSchema = exports.LegacyJobMetadataOutObject$ = exports.LegacyJobMetadataOutObject$outboundSchema = exports.LegacyJobMetadataOutObject$inboundSchema = exports.LegacyJobMetadataOutObject = void 0;
exports.legacyJobMetadataOutToJSON = legacyJobMetadataOutToJSON;
exports.legacyJobMetadataOutFromJSON = legacyJobMetadataOutFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
exports.LegacyJobMetadataOutObject = {
    JobMetadata: "job.metadata",
};
/** @internal */
exports.LegacyJobMetadataOutObject$inboundSchema = z.nativeEnum(exports.LegacyJobMetadataOutObject);
/** @internal */
exports.LegacyJobMetadataOutObject$outboundSchema = exports.LegacyJobMetadataOutObject$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var LegacyJobMetadataOutObject$;
(function (LegacyJobMetadataOutObject$) {
    /** @deprecated use `LegacyJobMetadataOutObject$inboundSchema` instead. */
    LegacyJobMetadataOutObject$.inboundSchema = exports.LegacyJobMetadataOutObject$inboundSchema;
    /** @deprecated use `LegacyJobMetadataOutObject$outboundSchema` instead. */
    LegacyJobMetadataOutObject$.outboundSchema = exports.LegacyJobMetadataOutObject$outboundSchema;
})(LegacyJobMetadataOutObject$ || (exports.LegacyJobMetadataOutObject$ = LegacyJobMetadataOutObject$ = {}));
/** @internal */
exports.LegacyJobMetadataOut$inboundSchema = z.object({
    expected_duration_seconds: z.nullable(z.number().int()).optional(),
    cost: z.nullable(z.number()).optional(),
    cost_currency: z.nullable(z.string()).optional(),
    train_tokens_per_step: z.nullable(z.number().int()).optional(),
    train_tokens: z.nullable(z.number().int()).optional(),
    data_tokens: z.nullable(z.number().int()).optional(),
    estimated_start_time: z.nullable(z.number().int()).optional(),
    deprecated: z.boolean().default(true),
    details: z.string(),
    epochs: z.nullable(z.number()).optional(),
    training_steps: z.nullable(z.number().int()).optional(),
    object: exports.LegacyJobMetadataOutObject$inboundSchema.default("job.metadata"),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "expected_duration_seconds": "expectedDurationSeconds",
        "cost_currency": "costCurrency",
        "train_tokens_per_step": "trainTokensPerStep",
        "train_tokens": "trainTokens",
        "data_tokens": "dataTokens",
        "estimated_start_time": "estimatedStartTime",
        "training_steps": "trainingSteps",
    });
});
/** @internal */
exports.LegacyJobMetadataOut$outboundSchema = z.object({
    expectedDurationSeconds: z.nullable(z.number().int()).optional(),
    cost: z.nullable(z.number()).optional(),
    costCurrency: z.nullable(z.string()).optional(),
    trainTokensPerStep: z.nullable(z.number().int()).optional(),
    trainTokens: z.nullable(z.number().int()).optional(),
    dataTokens: z.nullable(z.number().int()).optional(),
    estimatedStartTime: z.nullable(z.number().int()).optional(),
    deprecated: z.boolean().default(true),
    details: z.string(),
    epochs: z.nullable(z.number()).optional(),
    trainingSteps: z.nullable(z.number().int()).optional(),
    object: exports.LegacyJobMetadataOutObject$outboundSchema.default("job.metadata"),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        expectedDurationSeconds: "expected_duration_seconds",
        costCurrency: "cost_currency",
        trainTokensPerStep: "train_tokens_per_step",
        trainTokens: "train_tokens",
        dataTokens: "data_tokens",
        estimatedStartTime: "estimated_start_time",
        trainingSteps: "training_steps",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var LegacyJobMetadataOut$;
(function (LegacyJobMetadataOut$) {
    /** @deprecated use `LegacyJobMetadataOut$inboundSchema` instead. */
    LegacyJobMetadataOut$.inboundSchema = exports.LegacyJobMetadataOut$inboundSchema;
    /** @deprecated use `LegacyJobMetadataOut$outboundSchema` instead. */
    LegacyJobMetadataOut$.outboundSchema = exports.LegacyJobMetadataOut$outboundSchema;
})(LegacyJobMetadataOut$ || (exports.LegacyJobMetadataOut$ = LegacyJobMetadataOut$ = {}));
function legacyJobMetadataOutToJSON(legacyJobMetadataOut) {
    return JSON.stringify(exports.LegacyJobMetadataOut$outboundSchema.parse(legacyJobMetadataOut));
}
function legacyJobMetadataOutFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.LegacyJobMetadataOut$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'LegacyJobMetadataOut' from JSON`);
}
//# sourceMappingURL=legacyjobmetadataout.js.map