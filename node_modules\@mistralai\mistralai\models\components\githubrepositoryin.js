"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GithubRepositoryIn$ = exports.GithubRepositoryIn$outboundSchema = exports.GithubRepositoryIn$inboundSchema = exports.GithubRepositoryInType$ = exports.GithubRepositoryInType$outboundSchema = exports.GithubRepositoryInType$inboundSchema = exports.GithubRepositoryInType = void 0;
exports.githubRepositoryInToJSON = githubRepositoryInToJSON;
exports.githubRepositoryInFromJSON = githubRepositoryInFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
exports.GithubRepositoryInType = {
    Github: "github",
};
/** @internal */
exports.GithubRepositoryInType$inboundSchema = z.nativeEnum(exports.GithubRepositoryInType);
/** @internal */
exports.GithubRepositoryInType$outboundSchema = exports.GithubRepositoryInType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var GithubRepositoryInType$;
(function (GithubRepositoryInType$) {
    /** @deprecated use `GithubRepositoryInType$inboundSchema` instead. */
    GithubRepositoryInType$.inboundSchema = exports.GithubRepositoryInType$inboundSchema;
    /** @deprecated use `GithubRepositoryInType$outboundSchema` instead. */
    GithubRepositoryInType$.outboundSchema = exports.GithubRepositoryInType$outboundSchema;
})(GithubRepositoryInType$ || (exports.GithubRepositoryInType$ = GithubRepositoryInType$ = {}));
/** @internal */
exports.GithubRepositoryIn$inboundSchema = z.object({
    type: exports.GithubRepositoryInType$inboundSchema.default("github"),
    name: z.string(),
    owner: z.string(),
    ref: z.nullable(z.string()).optional(),
    weight: z.number().default(1),
    token: z.string(),
});
/** @internal */
exports.GithubRepositoryIn$outboundSchema = z.object({
    type: exports.GithubRepositoryInType$outboundSchema.default("github"),
    name: z.string(),
    owner: z.string(),
    ref: z.nullable(z.string()).optional(),
    weight: z.number().default(1),
    token: z.string(),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var GithubRepositoryIn$;
(function (GithubRepositoryIn$) {
    /** @deprecated use `GithubRepositoryIn$inboundSchema` instead. */
    GithubRepositoryIn$.inboundSchema = exports.GithubRepositoryIn$inboundSchema;
    /** @deprecated use `GithubRepositoryIn$outboundSchema` instead. */
    GithubRepositoryIn$.outboundSchema = exports.GithubRepositoryIn$outboundSchema;
})(GithubRepositoryIn$ || (exports.GithubRepositoryIn$ = GithubRepositoryIn$ = {}));
function githubRepositoryInToJSON(githubRepositoryIn) {
    return JSON.stringify(exports.GithubRepositoryIn$outboundSchema.parse(githubRepositoryIn));
}
function githubRepositoryInFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.GithubRepositoryIn$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'GithubRepositoryIn' from JSON`);
}
//# sourceMappingURL=githubrepositoryin.js.map