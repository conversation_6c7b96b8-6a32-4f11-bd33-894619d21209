import React, { useState, useEffect, useRef } from 'react';
import './App.css';

function App() {
  const [input, setInput] = useState('');
  const [provider, setProvider] = useState('mistral');
  const [model, setModel] = useState(''); // New state for model selection
  const [messages, setMessages] = useState([]);
  const [response, setResponse] = useState(null);
  const [loading, setLoading] = useState(false);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSend = async () => {
    if (!input.trim()) return;
    const newMessages = [...messages, { role: 'user', content: input }];
    setMessages(newMessages);
    setInput('');
    setLoading(true);
    setResponse(null);
    try {
      const requestBody = { messages: newMessages, provider };
      if (model) {
        requestBody.model = model; // Add model to request body if selected
      }
      const res = await fetch('http://localhost:3001/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
      });
      const data = await res.json();
      setResponse(data.result);
      setMessages([...newMessages, { role: 'assistant', content: data.result?.choices?.[0]?.message?.content || data.result }]);
    } catch (err) {
      setResponse('Error: ' + err.message);
    }
    setInput('');
    setLoading(false);
  };

  const handleProviderChange = (e) => {
    const selectedProvider = e.target.value;
    setProvider(selectedProvider);
    // Set default model based on provider, or clear if not applicable
    switch (selectedProvider) {
      case 'mistral':
        setModel('mistral-large-latest');
        break;
      case 'openrouter':
        setModel('deepseek/deepseek-r1-0528:free'); // Default for OpenRouter
        break;
      case 'gemini':
        setModel('gemini-2.0-flash'); // Default for Gemini
        break;
      default:
        setModel('');
    }
  };

  return (
    <div className="App">
      <div className="chat-container">
        <div className="chat-header">
          <h1>AI Chatbot</h1>
        </div>
        <div className="provider-select-container">
          <label htmlFor="provider-select">Choose a provider:</label>
          <select id="provider-select" value={provider} onChange={handleProviderChange}>
            <option value="mistral">Mistral</option>
            <option value="openrouter">OpenRouter</option>
            <option value="gemini">Gemini</option>
          </select>
          {(provider === 'openrouter' || provider === 'gemini') && (
            <input
              type="text"
              placeholder="Enter model (e.g., gpt-4, gemini-pro)"
              value={model}
              onChange={(e) => setModel(e.target.value)}
            />
          )}
        </div>
        <div className="messages-container">
          {messages.map((msg, i) => (
            <div key={i} className={`message ${msg.role}`}>
              {msg.content}
            </div>
          ))}
          {loading && <p className="loading-indicator">Loading...</p>}
          <div ref={messagesEndRef} />
        </div>
        <div className="input-area">
          <input
            type="text"
            value={input}
            onChange={e => setInput(e.target.value)}
            onKeyDown={e => e.key === 'Enter' && handleSend()}
            placeholder="Type your message..."
            disabled={loading}
          />
          <button onClick={handleSend} disabled={loading}>Send</button>
        </div>
      </div>
    </div>
  );
}
export default App;
