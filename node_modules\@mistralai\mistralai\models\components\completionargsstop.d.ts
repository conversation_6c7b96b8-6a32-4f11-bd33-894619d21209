import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type CompletionArgsStop = string | Array<string>;
/** @internal */
export declare const CompletionArgsStop$inboundSchema: z.ZodType<CompletionArgsStop, z.ZodTypeDef, unknown>;
/** @internal */
export type CompletionArgsStop$Outbound = string | Array<string>;
/** @internal */
export declare const CompletionArgsStop$outboundSchema: z.ZodType<CompletionArgsStop$Outbound, z.ZodTypeDef, CompletionArgsStop>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace CompletionArgsStop$ {
    /** @deprecated use `CompletionArgsStop$inboundSchema` instead. */
    const inboundSchema: z.ZodType<CompletionArgsStop, z.ZodTypeDef, unknown>;
    /** @deprecated use `CompletionArgsStop$outboundSchema` instead. */
    const outboundSchema: z.ZodType<CompletionArgsStop$Outbound, z.ZodTypeDef, CompletionArgsStop>;
    /** @deprecated use `CompletionArgsStop$Outbound` instead. */
    type Outbound = CompletionArgsStop$Outbound;
}
export declare function completionArgsStopToJSON(completionArgsStop: CompletionArgsStop): string;
export declare function completionArgsStopFromJSON(jsonString: string): SafeParseResult<CompletionArgsStop, SDKValidationError>;
//# sourceMappingURL=completionargsstop.d.ts.map