"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentUpdateRequest$ = exports.AgentUpdateRequest$outboundSchema = exports.AgentUpdateRequest$inboundSchema = exports.AgentUpdateRequestTools$ = exports.AgentUpdateRequestTools$outboundSchema = exports.AgentUpdateRequestTools$inboundSchema = void 0;
exports.agentUpdateRequestToolsToJSON = agentUpdateRequestToolsToJSON;
exports.agentUpdateRequestToolsFromJSON = agentUpdateRequestToolsFromJSON;
exports.agentUpdateRequestToJSON = agentUpdateRequestToJSON;
exports.agentUpdateRequestFromJSON = agentUpdateRequestFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const codeinterpretertool_js_1 = require("./codeinterpretertool.js");
const completionargs_js_1 = require("./completionargs.js");
const documentlibrarytool_js_1 = require("./documentlibrarytool.js");
const functiontool_js_1 = require("./functiontool.js");
const imagegenerationtool_js_1 = require("./imagegenerationtool.js");
const websearchpremiumtool_js_1 = require("./websearchpremiumtool.js");
const websearchtool_js_1 = require("./websearchtool.js");
/** @internal */
exports.AgentUpdateRequestTools$inboundSchema = z.union([
    codeinterpretertool_js_1.CodeInterpreterTool$inboundSchema.and(z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
        type: v.type,
    }))),
    imagegenerationtool_js_1.ImageGenerationTool$inboundSchema.and(z.object({ type: z.literal("image_generation") }).transform((v) => ({
        type: v.type,
    }))),
    websearchtool_js_1.WebSearchTool$inboundSchema.and(z.object({ type: z.literal("web_search") }).transform((v) => ({
        type: v.type,
    }))),
    websearchpremiumtool_js_1.WebSearchPremiumTool$inboundSchema.and(z.object({ type: z.literal("web_search_premium") }).transform((v) => ({
        type: v.type,
    }))),
    documentlibrarytool_js_1.DocumentLibraryTool$inboundSchema.and(z.object({ type: z.literal("document_library") }).transform((v) => ({
        type: v.type,
    }))),
    functiontool_js_1.FunctionTool$inboundSchema.and(z.object({ type: z.literal("function") }).transform((v) => ({
        type: v.type,
    }))),
]);
/** @internal */
exports.AgentUpdateRequestTools$outboundSchema = z.union([
    codeinterpretertool_js_1.CodeInterpreterTool$outboundSchema.and(z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
        type: v.type,
    }))),
    imagegenerationtool_js_1.ImageGenerationTool$outboundSchema.and(z.object({ type: z.literal("image_generation") }).transform((v) => ({
        type: v.type,
    }))),
    websearchtool_js_1.WebSearchTool$outboundSchema.and(z.object({ type: z.literal("web_search") }).transform((v) => ({
        type: v.type,
    }))),
    websearchpremiumtool_js_1.WebSearchPremiumTool$outboundSchema.and(z.object({ type: z.literal("web_search_premium") }).transform((v) => ({
        type: v.type,
    }))),
    documentlibrarytool_js_1.DocumentLibraryTool$outboundSchema.and(z.object({ type: z.literal("document_library") }).transform((v) => ({
        type: v.type,
    }))),
    functiontool_js_1.FunctionTool$outboundSchema.and(z.object({ type: z.literal("function") }).transform((v) => ({
        type: v.type,
    }))),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentUpdateRequestTools$;
(function (AgentUpdateRequestTools$) {
    /** @deprecated use `AgentUpdateRequestTools$inboundSchema` instead. */
    AgentUpdateRequestTools$.inboundSchema = exports.AgentUpdateRequestTools$inboundSchema;
    /** @deprecated use `AgentUpdateRequestTools$outboundSchema` instead. */
    AgentUpdateRequestTools$.outboundSchema = exports.AgentUpdateRequestTools$outboundSchema;
})(AgentUpdateRequestTools$ || (exports.AgentUpdateRequestTools$ = AgentUpdateRequestTools$ = {}));
function agentUpdateRequestToolsToJSON(agentUpdateRequestTools) {
    return JSON.stringify(exports.AgentUpdateRequestTools$outboundSchema.parse(agentUpdateRequestTools));
}
function agentUpdateRequestToolsFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.AgentUpdateRequestTools$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'AgentUpdateRequestTools' from JSON`);
}
/** @internal */
exports.AgentUpdateRequest$inboundSchema = z.object({
    instructions: z.nullable(z.string()).optional(),
    tools: z.array(z.union([
        codeinterpretertool_js_1.CodeInterpreterTool$inboundSchema.and(z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
            type: v.type,
        }))),
        imagegenerationtool_js_1.ImageGenerationTool$inboundSchema.and(z.object({ type: z.literal("image_generation") }).transform((v) => ({
            type: v.type,
        }))),
        websearchtool_js_1.WebSearchTool$inboundSchema.and(z.object({ type: z.literal("web_search") }).transform((v) => ({
            type: v.type,
        }))),
        websearchpremiumtool_js_1.WebSearchPremiumTool$inboundSchema.and(z.object({ type: z.literal("web_search_premium") }).transform((v) => ({
            type: v.type,
        }))),
        documentlibrarytool_js_1.DocumentLibraryTool$inboundSchema.and(z.object({ type: z.literal("document_library") }).transform((v) => ({
            type: v.type,
        }))),
        functiontool_js_1.FunctionTool$inboundSchema.and(z.object({ type: z.literal("function") }).transform((v) => ({
            type: v.type,
        }))),
    ])).optional(),
    completion_args: completionargs_js_1.CompletionArgs$inboundSchema.optional(),
    model: z.nullable(z.string()).optional(),
    name: z.nullable(z.string()).optional(),
    description: z.nullable(z.string()).optional(),
    handoffs: z.nullable(z.array(z.string())).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "completion_args": "completionArgs",
    });
});
/** @internal */
exports.AgentUpdateRequest$outboundSchema = z.object({
    instructions: z.nullable(z.string()).optional(),
    tools: z.array(z.union([
        codeinterpretertool_js_1.CodeInterpreterTool$outboundSchema.and(z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
            type: v.type,
        }))),
        imagegenerationtool_js_1.ImageGenerationTool$outboundSchema.and(z.object({ type: z.literal("image_generation") }).transform((v) => ({
            type: v.type,
        }))),
        websearchtool_js_1.WebSearchTool$outboundSchema.and(z.object({ type: z.literal("web_search") }).transform((v) => ({
            type: v.type,
        }))),
        websearchpremiumtool_js_1.WebSearchPremiumTool$outboundSchema.and(z.object({ type: z.literal("web_search_premium") }).transform((v) => ({
            type: v.type,
        }))),
        documentlibrarytool_js_1.DocumentLibraryTool$outboundSchema.and(z.object({ type: z.literal("document_library") }).transform((v) => ({
            type: v.type,
        }))),
        functiontool_js_1.FunctionTool$outboundSchema.and(z.object({ type: z.literal("function") }).transform((v) => ({
            type: v.type,
        }))),
    ])).optional(),
    completionArgs: completionargs_js_1.CompletionArgs$outboundSchema.optional(),
    model: z.nullable(z.string()).optional(),
    name: z.nullable(z.string()).optional(),
    description: z.nullable(z.string()).optional(),
    handoffs: z.nullable(z.array(z.string())).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        completionArgs: "completion_args",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentUpdateRequest$;
(function (AgentUpdateRequest$) {
    /** @deprecated use `AgentUpdateRequest$inboundSchema` instead. */
    AgentUpdateRequest$.inboundSchema = exports.AgentUpdateRequest$inboundSchema;
    /** @deprecated use `AgentUpdateRequest$outboundSchema` instead. */
    AgentUpdateRequest$.outboundSchema = exports.AgentUpdateRequest$outboundSchema;
})(AgentUpdateRequest$ || (exports.AgentUpdateRequest$ = AgentUpdateRequest$ = {}));
function agentUpdateRequestToJSON(agentUpdateRequest) {
    return JSON.stringify(exports.AgentUpdateRequest$outboundSchema.parse(agentUpdateRequest));
}
function agentUpdateRequestFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.AgentUpdateRequest$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'AgentUpdateRequest' from JSON`);
}
//# sourceMappingURL=agentupdaterequest.js.map