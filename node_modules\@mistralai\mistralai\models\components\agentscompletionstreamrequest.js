"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentsCompletionStreamRequest$ = exports.AgentsCompletionStreamRequest$outboundSchema = exports.AgentsCompletionStreamRequest$inboundSchema = exports.AgentsCompletionStreamRequestToolChoice$ = exports.AgentsCompletionStreamRequestToolChoice$outboundSchema = exports.AgentsCompletionStreamRequestToolChoice$inboundSchema = exports.AgentsCompletionStreamRequestMessages$ = exports.AgentsCompletionStreamRequestMessages$outboundSchema = exports.AgentsCompletionStreamRequestMessages$inboundSchema = exports.AgentsCompletionStreamRequestStop$ = exports.AgentsCompletionStreamRequestStop$outboundSchema = exports.AgentsCompletionStreamRequestStop$inboundSchema = void 0;
exports.agentsCompletionStreamRequestStopToJSON = agentsCompletionStreamRequestStopToJSON;
exports.agentsCompletionStreamRequestStopFromJSON = agentsCompletionStreamRequestStopFromJSON;
exports.agentsCompletionStreamRequestMessagesToJSON = agentsCompletionStreamRequestMessagesToJSON;
exports.agentsCompletionStreamRequestMessagesFromJSON = agentsCompletionStreamRequestMessagesFromJSON;
exports.agentsCompletionStreamRequestToolChoiceToJSON = agentsCompletionStreamRequestToolChoiceToJSON;
exports.agentsCompletionStreamRequestToolChoiceFromJSON = agentsCompletionStreamRequestToolChoiceFromJSON;
exports.agentsCompletionStreamRequestToJSON = agentsCompletionStreamRequestToJSON;
exports.agentsCompletionStreamRequestFromJSON = agentsCompletionStreamRequestFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const assistantmessage_js_1 = require("./assistantmessage.js");
const mistralpromptmode_js_1 = require("./mistralpromptmode.js");
const prediction_js_1 = require("./prediction.js");
const responseformat_js_1 = require("./responseformat.js");
const systemmessage_js_1 = require("./systemmessage.js");
const tool_js_1 = require("./tool.js");
const toolchoice_js_1 = require("./toolchoice.js");
const toolchoiceenum_js_1 = require("./toolchoiceenum.js");
const toolmessage_js_1 = require("./toolmessage.js");
const usermessage_js_1 = require("./usermessage.js");
/** @internal */
exports.AgentsCompletionStreamRequestStop$inboundSchema = z.union([z.string(), z.array(z.string())]);
/** @internal */
exports.AgentsCompletionStreamRequestStop$outboundSchema = z.union([z.string(), z.array(z.string())]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentsCompletionStreamRequestStop$;
(function (AgentsCompletionStreamRequestStop$) {
    /** @deprecated use `AgentsCompletionStreamRequestStop$inboundSchema` instead. */
    AgentsCompletionStreamRequestStop$.inboundSchema = exports.AgentsCompletionStreamRequestStop$inboundSchema;
    /** @deprecated use `AgentsCompletionStreamRequestStop$outboundSchema` instead. */
    AgentsCompletionStreamRequestStop$.outboundSchema = exports.AgentsCompletionStreamRequestStop$outboundSchema;
})(AgentsCompletionStreamRequestStop$ || (exports.AgentsCompletionStreamRequestStop$ = AgentsCompletionStreamRequestStop$ = {}));
function agentsCompletionStreamRequestStopToJSON(agentsCompletionStreamRequestStop) {
    return JSON.stringify(exports.AgentsCompletionStreamRequestStop$outboundSchema.parse(agentsCompletionStreamRequestStop));
}
function agentsCompletionStreamRequestStopFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.AgentsCompletionStreamRequestStop$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'AgentsCompletionStreamRequestStop' from JSON`);
}
/** @internal */
exports.AgentsCompletionStreamRequestMessages$inboundSchema = z.union([
    systemmessage_js_1.SystemMessage$inboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
        role: v.role,
    }))),
    usermessage_js_1.UserMessage$inboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({ role: v.role }))),
    assistantmessage_js_1.AssistantMessage$inboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
        role: v.role,
    }))),
    toolmessage_js_1.ToolMessage$inboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({ role: v.role }))),
]);
/** @internal */
exports.AgentsCompletionStreamRequestMessages$outboundSchema = z.union([
    systemmessage_js_1.SystemMessage$outboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
        role: v.role,
    }))),
    usermessage_js_1.UserMessage$outboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({ role: v.role }))),
    assistantmessage_js_1.AssistantMessage$outboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
        role: v.role,
    }))),
    toolmessage_js_1.ToolMessage$outboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({ role: v.role }))),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentsCompletionStreamRequestMessages$;
(function (AgentsCompletionStreamRequestMessages$) {
    /** @deprecated use `AgentsCompletionStreamRequestMessages$inboundSchema` instead. */
    AgentsCompletionStreamRequestMessages$.inboundSchema = exports.AgentsCompletionStreamRequestMessages$inboundSchema;
    /** @deprecated use `AgentsCompletionStreamRequestMessages$outboundSchema` instead. */
    AgentsCompletionStreamRequestMessages$.outboundSchema = exports.AgentsCompletionStreamRequestMessages$outboundSchema;
})(AgentsCompletionStreamRequestMessages$ || (exports.AgentsCompletionStreamRequestMessages$ = AgentsCompletionStreamRequestMessages$ = {}));
function agentsCompletionStreamRequestMessagesToJSON(agentsCompletionStreamRequestMessages) {
    return JSON.stringify(exports.AgentsCompletionStreamRequestMessages$outboundSchema.parse(agentsCompletionStreamRequestMessages));
}
function agentsCompletionStreamRequestMessagesFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.AgentsCompletionStreamRequestMessages$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'AgentsCompletionStreamRequestMessages' from JSON`);
}
/** @internal */
exports.AgentsCompletionStreamRequestToolChoice$inboundSchema = z.union([toolchoice_js_1.ToolChoice$inboundSchema, toolchoiceenum_js_1.ToolChoiceEnum$inboundSchema]);
/** @internal */
exports.AgentsCompletionStreamRequestToolChoice$outboundSchema = z.union([toolchoice_js_1.ToolChoice$outboundSchema, toolchoiceenum_js_1.ToolChoiceEnum$outboundSchema]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentsCompletionStreamRequestToolChoice$;
(function (AgentsCompletionStreamRequestToolChoice$) {
    /** @deprecated use `AgentsCompletionStreamRequestToolChoice$inboundSchema` instead. */
    AgentsCompletionStreamRequestToolChoice$.inboundSchema = exports.AgentsCompletionStreamRequestToolChoice$inboundSchema;
    /** @deprecated use `AgentsCompletionStreamRequestToolChoice$outboundSchema` instead. */
    AgentsCompletionStreamRequestToolChoice$.outboundSchema = exports.AgentsCompletionStreamRequestToolChoice$outboundSchema;
})(AgentsCompletionStreamRequestToolChoice$ || (exports.AgentsCompletionStreamRequestToolChoice$ = AgentsCompletionStreamRequestToolChoice$ = {}));
function agentsCompletionStreamRequestToolChoiceToJSON(agentsCompletionStreamRequestToolChoice) {
    return JSON.stringify(exports.AgentsCompletionStreamRequestToolChoice$outboundSchema.parse(agentsCompletionStreamRequestToolChoice));
}
function agentsCompletionStreamRequestToolChoiceFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.AgentsCompletionStreamRequestToolChoice$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'AgentsCompletionStreamRequestToolChoice' from JSON`);
}
/** @internal */
exports.AgentsCompletionStreamRequest$inboundSchema = z.object({
    max_tokens: z.nullable(z.number().int()).optional(),
    stream: z.boolean().default(true),
    stop: z.union([z.string(), z.array(z.string())]).optional(),
    random_seed: z.nullable(z.number().int()).optional(),
    messages: z.array(z.union([
        systemmessage_js_1.SystemMessage$inboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
            role: v.role,
        }))),
        usermessage_js_1.UserMessage$inboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({
            role: v.role,
        }))),
        assistantmessage_js_1.AssistantMessage$inboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
            role: v.role,
        }))),
        toolmessage_js_1.ToolMessage$inboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({
            role: v.role,
        }))),
    ])),
    response_format: responseformat_js_1.ResponseFormat$inboundSchema.optional(),
    tools: z.nullable(z.array(tool_js_1.Tool$inboundSchema)).optional(),
    tool_choice: z.union([toolchoice_js_1.ToolChoice$inboundSchema, toolchoiceenum_js_1.ToolChoiceEnum$inboundSchema])
        .optional(),
    presence_penalty: z.number().optional(),
    frequency_penalty: z.number().optional(),
    n: z.nullable(z.number().int()).optional(),
    prediction: prediction_js_1.Prediction$inboundSchema.optional(),
    parallel_tool_calls: z.boolean().optional(),
    prompt_mode: z.nullable(mistralpromptmode_js_1.MistralPromptMode$inboundSchema).optional(),
    agent_id: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "max_tokens": "maxTokens",
        "random_seed": "randomSeed",
        "response_format": "responseFormat",
        "tool_choice": "toolChoice",
        "presence_penalty": "presencePenalty",
        "frequency_penalty": "frequencyPenalty",
        "parallel_tool_calls": "parallelToolCalls",
        "prompt_mode": "promptMode",
        "agent_id": "agentId",
    });
});
/** @internal */
exports.AgentsCompletionStreamRequest$outboundSchema = z.object({
    maxTokens: z.nullable(z.number().int()).optional(),
    stream: z.boolean().default(true),
    stop: z.union([z.string(), z.array(z.string())]).optional(),
    randomSeed: z.nullable(z.number().int()).optional(),
    messages: z.array(z.union([
        systemmessage_js_1.SystemMessage$outboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
            role: v.role,
        }))),
        usermessage_js_1.UserMessage$outboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({
            role: v.role,
        }))),
        assistantmessage_js_1.AssistantMessage$outboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
            role: v.role,
        }))),
        toolmessage_js_1.ToolMessage$outboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({
            role: v.role,
        }))),
    ])),
    responseFormat: responseformat_js_1.ResponseFormat$outboundSchema.optional(),
    tools: z.nullable(z.array(tool_js_1.Tool$outboundSchema)).optional(),
    toolChoice: z.union([
        toolchoice_js_1.ToolChoice$outboundSchema,
        toolchoiceenum_js_1.ToolChoiceEnum$outboundSchema,
    ]).optional(),
    presencePenalty: z.number().optional(),
    frequencyPenalty: z.number().optional(),
    n: z.nullable(z.number().int()).optional(),
    prediction: prediction_js_1.Prediction$outboundSchema.optional(),
    parallelToolCalls: z.boolean().optional(),
    promptMode: z.nullable(mistralpromptmode_js_1.MistralPromptMode$outboundSchema).optional(),
    agentId: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        maxTokens: "max_tokens",
        randomSeed: "random_seed",
        responseFormat: "response_format",
        toolChoice: "tool_choice",
        presencePenalty: "presence_penalty",
        frequencyPenalty: "frequency_penalty",
        parallelToolCalls: "parallel_tool_calls",
        promptMode: "prompt_mode",
        agentId: "agent_id",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentsCompletionStreamRequest$;
(function (AgentsCompletionStreamRequest$) {
    /** @deprecated use `AgentsCompletionStreamRequest$inboundSchema` instead. */
    AgentsCompletionStreamRequest$.inboundSchema = exports.AgentsCompletionStreamRequest$inboundSchema;
    /** @deprecated use `AgentsCompletionStreamRequest$outboundSchema` instead. */
    AgentsCompletionStreamRequest$.outboundSchema = exports.AgentsCompletionStreamRequest$outboundSchema;
})(AgentsCompletionStreamRequest$ || (exports.AgentsCompletionStreamRequest$ = AgentsCompletionStreamRequest$ = {}));
function agentsCompletionStreamRequestToJSON(agentsCompletionStreamRequest) {
    return JSON.stringify(exports.AgentsCompletionStreamRequest$outboundSchema.parse(agentsCompletionStreamRequest));
}
function agentsCompletionStreamRequestFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.AgentsCompletionStreamRequest$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'AgentsCompletionStreamRequest' from JSON`);
}
//# sourceMappingURL=agentscompletionstreamrequest.js.map