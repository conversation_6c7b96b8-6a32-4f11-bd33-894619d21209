"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OCRPageObject$ = exports.OCRPageObject$outboundSchema = exports.OCRPageObject$inboundSchema = void 0;
exports.ocrPageObjectToJSON = ocrPageObjectToJSON;
exports.ocrPageObjectFromJSON = ocrPageObjectFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const ocrimageobject_js_1 = require("./ocrimageobject.js");
const ocrpagedimensions_js_1 = require("./ocrpagedimensions.js");
/** @internal */
exports.OCRPageObject$inboundSchema = z.object({
    index: z.number().int(),
    markdown: z.string(),
    images: z.array(ocrimageobject_js_1.OCRImageObject$inboundSchema),
    dimensions: z.nullable(ocrpagedimensions_js_1.OCRPageDimensions$inboundSchema),
});
/** @internal */
exports.OCRPageObject$outboundSchema = z.object({
    index: z.number().int(),
    markdown: z.string(),
    images: z.array(ocrimageobject_js_1.OCRImageObject$outboundSchema),
    dimensions: z.nullable(ocrpagedimensions_js_1.OCRPageDimensions$outboundSchema),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var OCRPageObject$;
(function (OCRPageObject$) {
    /** @deprecated use `OCRPageObject$inboundSchema` instead. */
    OCRPageObject$.inboundSchema = exports.OCRPageObject$inboundSchema;
    /** @deprecated use `OCRPageObject$outboundSchema` instead. */
    OCRPageObject$.outboundSchema = exports.OCRPageObject$outboundSchema;
})(OCRPageObject$ || (exports.OCRPageObject$ = OCRPageObject$ = {}));
function ocrPageObjectToJSON(ocrPageObject) {
    return JSON.stringify(exports.OCRPageObject$outboundSchema.parse(ocrPageObject));
}
function ocrPageObjectFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.OCRPageObject$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'OCRPageObject' from JSON`);
}
//# sourceMappingURL=ocrpageobject.js.map