"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobIn$ = exports.JobIn$outboundSchema = exports.JobIn$inboundSchema = exports.JobInRepositories$ = exports.JobInRepositories$outboundSchema = exports.JobInRepositories$inboundSchema = exports.Hyperparameters$ = exports.Hyperparameters$outboundSchema = exports.Hyperparameters$inboundSchema = exports.JobInIntegrations$ = exports.JobInIntegrations$outboundSchema = exports.JobInIntegrations$inboundSchema = void 0;
exports.jobInIntegrationsToJSON = jobInIntegrationsToJSON;
exports.jobInIntegrationsFromJSON = jobInIntegrationsFromJSON;
exports.hyperparametersToJSON = hyperparametersToJSON;
exports.hyperparametersFromJSON = hyperparametersFromJSON;
exports.jobInRepositoriesToJSON = jobInRepositoriesToJSON;
exports.jobInRepositoriesFromJSON = jobInRepositoriesFromJSON;
exports.jobInToJSON = jobInToJSON;
exports.jobInFromJSON = jobInFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const classifiertargetin_js_1 = require("./classifiertargetin.js");
const classifiertrainingparametersin_js_1 = require("./classifiertrainingparametersin.js");
const completiontrainingparametersin_js_1 = require("./completiontrainingparametersin.js");
const finetuneablemodeltype_js_1 = require("./finetuneablemodeltype.js");
const githubrepositoryin_js_1 = require("./githubrepositoryin.js");
const trainingfile_js_1 = require("./trainingfile.js");
const wandbintegration_js_1 = require("./wandbintegration.js");
/** @internal */
exports.JobInIntegrations$inboundSchema = wandbintegration_js_1.WandbIntegration$inboundSchema;
/** @internal */
exports.JobInIntegrations$outboundSchema = wandbintegration_js_1.WandbIntegration$outboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var JobInIntegrations$;
(function (JobInIntegrations$) {
    /** @deprecated use `JobInIntegrations$inboundSchema` instead. */
    JobInIntegrations$.inboundSchema = exports.JobInIntegrations$inboundSchema;
    /** @deprecated use `JobInIntegrations$outboundSchema` instead. */
    JobInIntegrations$.outboundSchema = exports.JobInIntegrations$outboundSchema;
})(JobInIntegrations$ || (exports.JobInIntegrations$ = JobInIntegrations$ = {}));
function jobInIntegrationsToJSON(jobInIntegrations) {
    return JSON.stringify(exports.JobInIntegrations$outboundSchema.parse(jobInIntegrations));
}
function jobInIntegrationsFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.JobInIntegrations$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'JobInIntegrations' from JSON`);
}
/** @internal */
exports.Hyperparameters$inboundSchema = z.union([
    classifiertrainingparametersin_js_1.ClassifierTrainingParametersIn$inboundSchema,
    completiontrainingparametersin_js_1.CompletionTrainingParametersIn$inboundSchema,
]);
/** @internal */
exports.Hyperparameters$outboundSchema = z.union([
    classifiertrainingparametersin_js_1.ClassifierTrainingParametersIn$outboundSchema,
    completiontrainingparametersin_js_1.CompletionTrainingParametersIn$outboundSchema,
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Hyperparameters$;
(function (Hyperparameters$) {
    /** @deprecated use `Hyperparameters$inboundSchema` instead. */
    Hyperparameters$.inboundSchema = exports.Hyperparameters$inboundSchema;
    /** @deprecated use `Hyperparameters$outboundSchema` instead. */
    Hyperparameters$.outboundSchema = exports.Hyperparameters$outboundSchema;
})(Hyperparameters$ || (exports.Hyperparameters$ = Hyperparameters$ = {}));
function hyperparametersToJSON(hyperparameters) {
    return JSON.stringify(exports.Hyperparameters$outboundSchema.parse(hyperparameters));
}
function hyperparametersFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.Hyperparameters$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'Hyperparameters' from JSON`);
}
/** @internal */
exports.JobInRepositories$inboundSchema = githubrepositoryin_js_1.GithubRepositoryIn$inboundSchema;
/** @internal */
exports.JobInRepositories$outboundSchema = githubrepositoryin_js_1.GithubRepositoryIn$outboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var JobInRepositories$;
(function (JobInRepositories$) {
    /** @deprecated use `JobInRepositories$inboundSchema` instead. */
    JobInRepositories$.inboundSchema = exports.JobInRepositories$inboundSchema;
    /** @deprecated use `JobInRepositories$outboundSchema` instead. */
    JobInRepositories$.outboundSchema = exports.JobInRepositories$outboundSchema;
})(JobInRepositories$ || (exports.JobInRepositories$ = JobInRepositories$ = {}));
function jobInRepositoriesToJSON(jobInRepositories) {
    return JSON.stringify(exports.JobInRepositories$outboundSchema.parse(jobInRepositories));
}
function jobInRepositoriesFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.JobInRepositories$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'JobInRepositories' from JSON`);
}
/** @internal */
exports.JobIn$inboundSchema = z
    .object({
    model: z.string(),
    training_files: z.array(trainingfile_js_1.TrainingFile$inboundSchema).optional(),
    validation_files: z.nullable(z.array(z.string())).optional(),
    suffix: z.nullable(z.string()).optional(),
    integrations: z.nullable(z.array(wandbintegration_js_1.WandbIntegration$inboundSchema))
        .optional(),
    auto_start: z.boolean().optional(),
    invalid_sample_skip_percentage: z.number().default(0),
    job_type: z.nullable(finetuneablemodeltype_js_1.FineTuneableModelType$inboundSchema).optional(),
    hyperparameters: z.union([
        classifiertrainingparametersin_js_1.ClassifierTrainingParametersIn$inboundSchema,
        completiontrainingparametersin_js_1.CompletionTrainingParametersIn$inboundSchema,
    ]),
    repositories: z.nullable(z.array(githubrepositoryin_js_1.GithubRepositoryIn$inboundSchema))
        .optional(),
    classifier_targets: z.nullable(z.array(classifiertargetin_js_1.ClassifierTargetIn$inboundSchema))
        .optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "training_files": "trainingFiles",
        "validation_files": "validationFiles",
        "auto_start": "autoStart",
        "invalid_sample_skip_percentage": "invalidSampleSkipPercentage",
        "job_type": "jobType",
        "classifier_targets": "classifierTargets",
    });
});
/** @internal */
exports.JobIn$outboundSchema = z.object({
    model: z.string(),
    trainingFiles: z.array(trainingfile_js_1.TrainingFile$outboundSchema).optional(),
    validationFiles: z.nullable(z.array(z.string())).optional(),
    suffix: z.nullable(z.string()).optional(),
    integrations: z.nullable(z.array(wandbintegration_js_1.WandbIntegration$outboundSchema)).optional(),
    autoStart: z.boolean().optional(),
    invalidSampleSkipPercentage: z.number().default(0),
    jobType: z.nullable(finetuneablemodeltype_js_1.FineTuneableModelType$outboundSchema).optional(),
    hyperparameters: z.union([
        classifiertrainingparametersin_js_1.ClassifierTrainingParametersIn$outboundSchema,
        completiontrainingparametersin_js_1.CompletionTrainingParametersIn$outboundSchema,
    ]),
    repositories: z.nullable(z.array(githubrepositoryin_js_1.GithubRepositoryIn$outboundSchema))
        .optional(),
    classifierTargets: z.nullable(z.array(classifiertargetin_js_1.ClassifierTargetIn$outboundSchema))
        .optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        trainingFiles: "training_files",
        validationFiles: "validation_files",
        autoStart: "auto_start",
        invalidSampleSkipPercentage: "invalid_sample_skip_percentage",
        jobType: "job_type",
        classifierTargets: "classifier_targets",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var JobIn$;
(function (JobIn$) {
    /** @deprecated use `JobIn$inboundSchema` instead. */
    JobIn$.inboundSchema = exports.JobIn$inboundSchema;
    /** @deprecated use `JobIn$outboundSchema` instead. */
    JobIn$.outboundSchema = exports.JobIn$outboundSchema;
})(JobIn$ || (exports.JobIn$ = JobIn$ = {}));
function jobInToJSON(jobIn) {
    return JSON.stringify(exports.JobIn$outboundSchema.parse(jobIn));
}
function jobInFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.JobIn$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'JobIn' from JSON`);
}
//# sourceMappingURL=jobin.js.map