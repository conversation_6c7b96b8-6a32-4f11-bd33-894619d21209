"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InputEntries$ = exports.InputEntries$outboundSchema = exports.InputEntries$inboundSchema = void 0;
exports.inputEntriesToJSON = inputEntriesToJSON;
exports.inputEntriesFromJSON = inputEntriesFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const functionresultentry_js_1 = require("./functionresultentry.js");
const messageinputentry_js_1 = require("./messageinputentry.js");
/** @internal */
exports.InputEntries$inboundSchema = z.union([
    messageinputentry_js_1.MessageInputEntry$inboundSchema,
    functionresultentry_js_1.FunctionResultEntry$inboundSchema,
]);
/** @internal */
exports.InputEntries$outboundSchema = z.union([
    messageinputentry_js_1.MessageInputEntry$outboundSchema,
    functionresultentry_js_1.FunctionResultEntry$outboundSchema,
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var InputEntries$;
(function (InputEntries$) {
    /** @deprecated use `InputEntries$inboundSchema` instead. */
    InputEntries$.inboundSchema = exports.InputEntries$inboundSchema;
    /** @deprecated use `InputEntries$outboundSchema` instead. */
    InputEntries$.outboundSchema = exports.InputEntries$outboundSchema;
})(InputEntries$ || (exports.InputEntries$ = InputEntries$ = {}));
function inputEntriesToJSON(inputEntries) {
    return JSON.stringify(exports.InputEntries$outboundSchema.parse(inputEntries));
}
function inputEntriesFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.InputEntries$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'InputEntries' from JSON`);
}
//# sourceMappingURL=inputentries.js.map