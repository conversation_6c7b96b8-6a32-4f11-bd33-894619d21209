"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationMessages$ = exports.ConversationMessages$outboundSchema = exports.ConversationMessages$inboundSchema = exports.ConversationMessagesObject$ = exports.ConversationMessagesObject$outboundSchema = exports.ConversationMessagesObject$inboundSchema = exports.ConversationMessagesObject = void 0;
exports.conversationMessagesToJSON = conversationMessagesToJSON;
exports.conversationMessagesFromJSON = conversationMessagesFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const messageentries_js_1 = require("./messageentries.js");
exports.ConversationMessagesObject = {
    ConversationMessages: "conversation.messages",
};
/** @internal */
exports.ConversationMessagesObject$inboundSchema = z.nativeEnum(exports.ConversationMessagesObject);
/** @internal */
exports.ConversationMessagesObject$outboundSchema = exports.ConversationMessagesObject$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationMessagesObject$;
(function (ConversationMessagesObject$) {
    /** @deprecated use `ConversationMessagesObject$inboundSchema` instead. */
    ConversationMessagesObject$.inboundSchema = exports.ConversationMessagesObject$inboundSchema;
    /** @deprecated use `ConversationMessagesObject$outboundSchema` instead. */
    ConversationMessagesObject$.outboundSchema = exports.ConversationMessagesObject$outboundSchema;
})(ConversationMessagesObject$ || (exports.ConversationMessagesObject$ = ConversationMessagesObject$ = {}));
/** @internal */
exports.ConversationMessages$inboundSchema = z.object({
    object: exports.ConversationMessagesObject$inboundSchema.default("conversation.messages"),
    conversation_id: z.string(),
    messages: z.array(messageentries_js_1.MessageEntries$inboundSchema),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "conversation_id": "conversationId",
    });
});
/** @internal */
exports.ConversationMessages$outboundSchema = z.object({
    object: exports.ConversationMessagesObject$outboundSchema.default("conversation.messages"),
    conversationId: z.string(),
    messages: z.array(messageentries_js_1.MessageEntries$outboundSchema),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        conversationId: "conversation_id",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationMessages$;
(function (ConversationMessages$) {
    /** @deprecated use `ConversationMessages$inboundSchema` instead. */
    ConversationMessages$.inboundSchema = exports.ConversationMessages$inboundSchema;
    /** @deprecated use `ConversationMessages$outboundSchema` instead. */
    ConversationMessages$.outboundSchema = exports.ConversationMessages$outboundSchema;
})(ConversationMessages$ || (exports.ConversationMessages$ = ConversationMessages$ = {}));
function conversationMessagesToJSON(conversationMessages) {
    return JSON.stringify(exports.ConversationMessages$outboundSchema.parse(conversationMessages));
}
function conversationMessagesFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ConversationMessages$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ConversationMessages' from JSON`);
}
//# sourceMappingURL=conversationmessages.js.map