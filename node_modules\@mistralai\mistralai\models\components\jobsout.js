"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobsOut$ = exports.JobsOut$outboundSchema = exports.JobsOut$inboundSchema = exports.JobsOutObject$ = exports.JobsOutObject$outboundSchema = exports.JobsOutObject$inboundSchema = exports.JobsOutData$ = exports.JobsOutData$outboundSchema = exports.JobsOutData$inboundSchema = exports.JobsOutObject = void 0;
exports.jobsOutDataToJSON = jobsOutDataToJSON;
exports.jobsOutDataFromJSON = jobsOutDataFromJSON;
exports.jobsOutToJSON = jobsOutToJSON;
exports.jobsOutFromJSON = jobsOutFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const classifierjobout_js_1 = require("./classifierjobout.js");
const completionjobout_js_1 = require("./completionjobout.js");
exports.JobsOutObject = {
    List: "list",
};
/** @internal */
exports.JobsOutData$inboundSchema = z.union([
    classifierjobout_js_1.ClassifierJobOut$inboundSchema.and(z.object({ job_type: z.literal("classifier") }).transform((v) => ({
        jobType: v.job_type,
    }))),
    completionjobout_js_1.CompletionJobOut$inboundSchema.and(z.object({ job_type: z.literal("completion") }).transform((v) => ({
        jobType: v.job_type,
    }))),
]);
/** @internal */
exports.JobsOutData$outboundSchema = z.union([
    classifierjobout_js_1.ClassifierJobOut$outboundSchema.and(z.object({ jobType: z.literal("classifier") }).transform((v) => ({
        job_type: v.jobType,
    }))),
    completionjobout_js_1.CompletionJobOut$outboundSchema.and(z.object({ jobType: z.literal("completion") }).transform((v) => ({
        job_type: v.jobType,
    }))),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var JobsOutData$;
(function (JobsOutData$) {
    /** @deprecated use `JobsOutData$inboundSchema` instead. */
    JobsOutData$.inboundSchema = exports.JobsOutData$inboundSchema;
    /** @deprecated use `JobsOutData$outboundSchema` instead. */
    JobsOutData$.outboundSchema = exports.JobsOutData$outboundSchema;
})(JobsOutData$ || (exports.JobsOutData$ = JobsOutData$ = {}));
function jobsOutDataToJSON(jobsOutData) {
    return JSON.stringify(exports.JobsOutData$outboundSchema.parse(jobsOutData));
}
function jobsOutDataFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.JobsOutData$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'JobsOutData' from JSON`);
}
/** @internal */
exports.JobsOutObject$inboundSchema = z.nativeEnum(exports.JobsOutObject);
/** @internal */
exports.JobsOutObject$outboundSchema = exports.JobsOutObject$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var JobsOutObject$;
(function (JobsOutObject$) {
    /** @deprecated use `JobsOutObject$inboundSchema` instead. */
    JobsOutObject$.inboundSchema = exports.JobsOutObject$inboundSchema;
    /** @deprecated use `JobsOutObject$outboundSchema` instead. */
    JobsOutObject$.outboundSchema = exports.JobsOutObject$outboundSchema;
})(JobsOutObject$ || (exports.JobsOutObject$ = JobsOutObject$ = {}));
/** @internal */
exports.JobsOut$inboundSchema = z.object({
    data: z.array(z.union([
        classifierjobout_js_1.ClassifierJobOut$inboundSchema.and(z.object({ job_type: z.literal("classifier") }).transform((v) => ({
            jobType: v.job_type,
        }))),
        completionjobout_js_1.CompletionJobOut$inboundSchema.and(z.object({ job_type: z.literal("completion") }).transform((v) => ({
            jobType: v.job_type,
        }))),
    ])).optional(),
    object: exports.JobsOutObject$inboundSchema.default("list"),
    total: z.number().int(),
});
/** @internal */
exports.JobsOut$outboundSchema = z.object({
    data: z.array(z.union([
        classifierjobout_js_1.ClassifierJobOut$outboundSchema.and(z.object({ jobType: z.literal("classifier") }).transform((v) => ({
            job_type: v.jobType,
        }))),
        completionjobout_js_1.CompletionJobOut$outboundSchema.and(z.object({ jobType: z.literal("completion") }).transform((v) => ({
            job_type: v.jobType,
        }))),
    ])).optional(),
    object: exports.JobsOutObject$outboundSchema.default("list"),
    total: z.number().int(),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var JobsOut$;
(function (JobsOut$) {
    /** @deprecated use `JobsOut$inboundSchema` instead. */
    JobsOut$.inboundSchema = exports.JobsOut$inboundSchema;
    /** @deprecated use `JobsOut$outboundSchema` instead. */
    JobsOut$.outboundSchema = exports.JobsOut$outboundSchema;
})(JobsOut$ || (exports.JobsOut$ = JobsOut$ = {}));
function jobsOutToJSON(jobsOut) {
    return JSON.stringify(exports.JobsOut$outboundSchema.parse(jobsOut));
}
function jobsOutFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.JobsOut$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'JobsOut' from JSON`);
}
//# sourceMappingURL=jobsout.js.map