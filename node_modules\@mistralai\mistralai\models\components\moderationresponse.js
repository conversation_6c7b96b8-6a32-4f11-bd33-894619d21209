"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModerationResponse$ = exports.ModerationResponse$outboundSchema = exports.ModerationResponse$inboundSchema = void 0;
exports.moderationResponseToJSON = moderationResponseToJSON;
exports.moderationResponseFromJSON = moderationResponseFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const moderationobject_js_1 = require("./moderationobject.js");
/** @internal */
exports.ModerationResponse$inboundSchema = z.object({
    id: z.string(),
    model: z.string(),
    results: z.array(moderationobject_js_1.ModerationObject$inboundSchema),
});
/** @internal */
exports.ModerationResponse$outboundSchema = z.object({
    id: z.string(),
    model: z.string(),
    results: z.array(moderationobject_js_1.ModerationObject$outboundSchema),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ModerationResponse$;
(function (ModerationResponse$) {
    /** @deprecated use `ModerationResponse$inboundSchema` instead. */
    ModerationResponse$.inboundSchema = exports.ModerationResponse$inboundSchema;
    /** @deprecated use `ModerationResponse$outboundSchema` instead. */
    ModerationResponse$.outboundSchema = exports.ModerationResponse$outboundSchema;
})(ModerationResponse$ || (exports.ModerationResponse$ = ModerationResponse$ = {}));
function moderationResponseToJSON(moderationResponse) {
    return JSON.stringify(exports.ModerationResponse$outboundSchema.parse(moderationResponse));
}
function moderationResponseFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ModerationResponse$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ModerationResponse' from JSON`);
}
//# sourceMappingURL=moderationresponse.js.map