"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompletionEvent$ = exports.CompletionEvent$outboundSchema = exports.CompletionEvent$inboundSchema = void 0;
exports.completionEventToJSON = completionEventToJSON;
exports.completionEventFromJSON = completionEventFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const completionchunk_js_1 = require("./completionchunk.js");
/** @internal */
exports.CompletionEvent$inboundSchema = z.object({
    data: z.string().transform((v, ctx) => {
        try {
            return JSON.parse(v);
        }
        catch (err) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: `malformed json: ${err}`,
            });
            return z.NEVER;
        }
    }).pipe(completionchunk_js_1.CompletionChunk$inboundSchema),
});
/** @internal */
exports.CompletionEvent$outboundSchema = z.object({
    data: completionchunk_js_1.CompletionChunk$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var CompletionEvent$;
(function (CompletionEvent$) {
    /** @deprecated use `CompletionEvent$inboundSchema` instead. */
    CompletionEvent$.inboundSchema = exports.CompletionEvent$inboundSchema;
    /** @deprecated use `CompletionEvent$outboundSchema` instead. */
    CompletionEvent$.outboundSchema = exports.CompletionEvent$outboundSchema;
})(CompletionEvent$ || (exports.CompletionEvent$ = CompletionEvent$ = {}));
function completionEventToJSON(completionEvent) {
    return JSON.stringify(exports.CompletionEvent$outboundSchema.parse(completionEvent));
}
function completionEventFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.CompletionEvent$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'CompletionEvent' from JSON`);
}
//# sourceMappingURL=completionevent.js.map