"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseErrorEvent$ = exports.ResponseErrorEvent$outboundSchema = exports.ResponseErrorEvent$inboundSchema = exports.ResponseErrorEventType$ = exports.ResponseErrorEventType$outboundSchema = exports.ResponseErrorEventType$inboundSchema = exports.ResponseErrorEventType = void 0;
exports.responseErrorEventToJSON = responseErrorEventToJSON;
exports.responseErrorEventFromJSON = responseErrorEventFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
exports.ResponseErrorEventType = {
    ConversationResponseError: "conversation.response.error",
};
/** @internal */
exports.ResponseErrorEventType$inboundSchema = z.nativeEnum(exports.ResponseErrorEventType);
/** @internal */
exports.ResponseErrorEventType$outboundSchema = exports.ResponseErrorEventType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ResponseErrorEventType$;
(function (ResponseErrorEventType$) {
    /** @deprecated use `ResponseErrorEventType$inboundSchema` instead. */
    ResponseErrorEventType$.inboundSchema = exports.ResponseErrorEventType$inboundSchema;
    /** @deprecated use `ResponseErrorEventType$outboundSchema` instead. */
    ResponseErrorEventType$.outboundSchema = exports.ResponseErrorEventType$outboundSchema;
})(ResponseErrorEventType$ || (exports.ResponseErrorEventType$ = ResponseErrorEventType$ = {}));
/** @internal */
exports.ResponseErrorEvent$inboundSchema = z.object({
    type: exports.ResponseErrorEventType$inboundSchema.default("conversation.response.error"),
    created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
        .optional(),
    message: z.string(),
    code: z.number().int(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "created_at": "createdAt",
    });
});
/** @internal */
exports.ResponseErrorEvent$outboundSchema = z.object({
    type: exports.ResponseErrorEventType$outboundSchema.default("conversation.response.error"),
    createdAt: z.date().transform(v => v.toISOString()).optional(),
    message: z.string(),
    code: z.number().int(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        createdAt: "created_at",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ResponseErrorEvent$;
(function (ResponseErrorEvent$) {
    /** @deprecated use `ResponseErrorEvent$inboundSchema` instead. */
    ResponseErrorEvent$.inboundSchema = exports.ResponseErrorEvent$inboundSchema;
    /** @deprecated use `ResponseErrorEvent$outboundSchema` instead. */
    ResponseErrorEvent$.outboundSchema = exports.ResponseErrorEvent$outboundSchema;
})(ResponseErrorEvent$ || (exports.ResponseErrorEvent$ = ResponseErrorEvent$ = {}));
function responseErrorEventToJSON(responseErrorEvent) {
    return JSON.stringify(exports.ResponseErrorEvent$outboundSchema.parse(responseErrorEvent));
}
function responseErrorEventFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ResponseErrorEvent$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ResponseErrorEvent' from JSON`);
}
//# sourceMappingURL=responseerrorevent.js.map