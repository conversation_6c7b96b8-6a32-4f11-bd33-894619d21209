"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JsonSchema$ = exports.JsonSchema$outboundSchema = exports.JsonSchema$inboundSchema = void 0;
exports.jsonSchemaToJSON = jsonSchemaToJSON;
exports.jsonSchemaFromJSON = jsonSchemaFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.JsonSchema$inboundSchema = z.object({
    name: z.string(),
    description: z.nullable(z.string()).optional(),
    schema: z.record(z.any()),
    strict: z.boolean().optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "schema": "schemaDefinition",
    });
});
/** @internal */
exports.JsonSchema$outboundSchema = z.object({
    name: z.string(),
    description: z.nullable(z.string()).optional(),
    schemaDefinition: z.record(z.any()),
    strict: z.boolean().optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        schemaDefinition: "schema",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var JsonSchema$;
(function (JsonSchema$) {
    /** @deprecated use `JsonSchema$inboundSchema` instead. */
    JsonSchema$.inboundSchema = exports.JsonSchema$inboundSchema;
    /** @deprecated use `JsonSchema$outboundSchema` instead. */
    JsonSchema$.outboundSchema = exports.JsonSchema$outboundSchema;
})(JsonSchema$ || (exports.JsonSchema$ = JsonSchema$ = {}));
function jsonSchemaToJSON(jsonSchema) {
    return JSON.stringify(exports.JsonSchema$outboundSchema.parse(jsonSchema));
}
function jsonSchemaFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.JsonSchema$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'JsonSchema' from JSON`);
}
//# sourceMappingURL=jsonschema.js.map