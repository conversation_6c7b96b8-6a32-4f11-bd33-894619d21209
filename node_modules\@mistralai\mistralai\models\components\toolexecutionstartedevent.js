"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolExecutionStartedEvent$ = exports.ToolExecutionStartedEvent$outboundSchema = exports.ToolExecutionStartedEvent$inboundSchema = exports.ToolExecutionStartedEventType$ = exports.ToolExecutionStartedEventType$outboundSchema = exports.ToolExecutionStartedEventType$inboundSchema = exports.ToolExecutionStartedEventType = void 0;
exports.toolExecutionStartedEventToJSON = toolExecutionStartedEventToJSON;
exports.toolExecutionStartedEventFromJSON = toolExecutionStartedEventFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const builtinconnectors_js_1 = require("./builtinconnectors.js");
exports.ToolExecutionStartedEventType = {
    ToolExecutionStarted: "tool.execution.started",
};
/** @internal */
exports.ToolExecutionStartedEventType$inboundSchema = z.nativeEnum(exports.ToolExecutionStartedEventType);
/** @internal */
exports.ToolExecutionStartedEventType$outboundSchema = exports.ToolExecutionStartedEventType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ToolExecutionStartedEventType$;
(function (ToolExecutionStartedEventType$) {
    /** @deprecated use `ToolExecutionStartedEventType$inboundSchema` instead. */
    ToolExecutionStartedEventType$.inboundSchema = exports.ToolExecutionStartedEventType$inboundSchema;
    /** @deprecated use `ToolExecutionStartedEventType$outboundSchema` instead. */
    ToolExecutionStartedEventType$.outboundSchema = exports.ToolExecutionStartedEventType$outboundSchema;
})(ToolExecutionStartedEventType$ || (exports.ToolExecutionStartedEventType$ = ToolExecutionStartedEventType$ = {}));
/** @internal */
exports.ToolExecutionStartedEvent$inboundSchema = z.object({
    type: exports.ToolExecutionStartedEventType$inboundSchema.default("tool.execution.started"),
    created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
        .optional(),
    output_index: z.number().int().default(0),
    id: z.string(),
    name: builtinconnectors_js_1.BuiltInConnectors$inboundSchema,
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "created_at": "createdAt",
        "output_index": "outputIndex",
    });
});
/** @internal */
exports.ToolExecutionStartedEvent$outboundSchema = z.object({
    type: exports.ToolExecutionStartedEventType$outboundSchema.default("tool.execution.started"),
    createdAt: z.date().transform(v => v.toISOString()).optional(),
    outputIndex: z.number().int().default(0),
    id: z.string(),
    name: builtinconnectors_js_1.BuiltInConnectors$outboundSchema,
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        createdAt: "created_at",
        outputIndex: "output_index",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ToolExecutionStartedEvent$;
(function (ToolExecutionStartedEvent$) {
    /** @deprecated use `ToolExecutionStartedEvent$inboundSchema` instead. */
    ToolExecutionStartedEvent$.inboundSchema = exports.ToolExecutionStartedEvent$inboundSchema;
    /** @deprecated use `ToolExecutionStartedEvent$outboundSchema` instead. */
    ToolExecutionStartedEvent$.outboundSchema = exports.ToolExecutionStartedEvent$outboundSchema;
})(ToolExecutionStartedEvent$ || (exports.ToolExecutionStartedEvent$ = ToolExecutionStartedEvent$ = {}));
function toolExecutionStartedEventToJSON(toolExecutionStartedEvent) {
    return JSON.stringify(exports.ToolExecutionStartedEvent$outboundSchema.parse(toolExecutionStartedEvent));
}
function toolExecutionStartedEventFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ToolExecutionStartedEvent$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ToolExecutionStartedEvent' from JSON`);
}
//# sourceMappingURL=toolexecutionstartedevent.js.map