{"version": 3, "file": "contentchunk.js", "sourceRoot": "", "sources": ["../../src/models/components/contentchunk.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;AA6GH,gDAEC;AAED,oDAQC;AAvHD,uCAAyB;AACzB,qDAAiD;AAGjD,+DAK+B;AAC/B,yDAK4B;AAC5B,2DAK6B;AAC7B,iDAKwB;AAQxB,gBAAgB;AACH,QAAA,0BAA0B,GAInC,CAAC,CAAC,KAAK,CAAC;IACV,8CAA2B,CAAC,GAAG,CAC7B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,sCAAuB,CAAC,GAAG,CACzB,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAC3E;IACD,gDAA4B,CAAC,GAAG,CAC9B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,oDAA8B,CAAC,GAAG,CAChC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC9D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;CACF,CAAC,CAAC;AASH,gBAAgB;AACH,QAAA,2BAA2B,GAIpC,CAAC,CAAC,KAAK,CAAC;IACV,+CAA4B,CAAC,GAAG,CAC9B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,uCAAwB,CAAC,GAAG,CAC1B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAC3E;IACD,iDAA6B,CAAC,GAAG,CAC/B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,qDAA+B,CAAC,GAAG,CACjC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC9D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;CACF,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,aAAa,CAO7B;AAPD,WAAiB,aAAa;IAC5B,4DAA4D;IAC/C,2BAAa,GAAG,kCAA0B,CAAC;IACxD,6DAA6D;IAChD,4BAAc,GAAG,mCAA2B,CAAC;AAG5D,CAAC,EAPgB,aAAa,6BAAb,aAAa,QAO7B;AAED,SAAgB,kBAAkB,CAAC,YAA0B;IAC3D,OAAO,IAAI,CAAC,SAAS,CAAC,mCAA2B,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;AACzE,CAAC;AAED,SAAgB,oBAAoB,CAClC,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,kCAA0B,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACtD,0CAA0C,CAC3C,CAAC;AACJ,CAAC"}