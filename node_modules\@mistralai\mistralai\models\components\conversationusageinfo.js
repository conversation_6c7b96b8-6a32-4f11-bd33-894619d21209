"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationUsageInfo$ = exports.ConversationUsageInfo$outboundSchema = exports.ConversationUsageInfo$inboundSchema = void 0;
exports.conversationUsageInfoToJSON = conversationUsageInfoToJSON;
exports.conversationUsageInfoFromJSON = conversationUsageInfoFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.ConversationUsageInfo$inboundSchema = z.object({
    prompt_tokens: z.number().int().default(0),
    completion_tokens: z.number().int().default(0),
    total_tokens: z.number().int().default(0),
    connector_tokens: z.nullable(z.number().int()).optional(),
    connectors: z.nullable(z.record(z.number().int())).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "prompt_tokens": "promptTokens",
        "completion_tokens": "completionTokens",
        "total_tokens": "totalTokens",
        "connector_tokens": "connectorTokens",
    });
});
/** @internal */
exports.ConversationUsageInfo$outboundSchema = z.object({
    promptTokens: z.number().int().default(0),
    completionTokens: z.number().int().default(0),
    totalTokens: z.number().int().default(0),
    connectorTokens: z.nullable(z.number().int()).optional(),
    connectors: z.nullable(z.record(z.number().int())).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        promptTokens: "prompt_tokens",
        completionTokens: "completion_tokens",
        totalTokens: "total_tokens",
        connectorTokens: "connector_tokens",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationUsageInfo$;
(function (ConversationUsageInfo$) {
    /** @deprecated use `ConversationUsageInfo$inboundSchema` instead. */
    ConversationUsageInfo$.inboundSchema = exports.ConversationUsageInfo$inboundSchema;
    /** @deprecated use `ConversationUsageInfo$outboundSchema` instead. */
    ConversationUsageInfo$.outboundSchema = exports.ConversationUsageInfo$outboundSchema;
})(ConversationUsageInfo$ || (exports.ConversationUsageInfo$ = ConversationUsageInfo$ = {}));
function conversationUsageInfoToJSON(conversationUsageInfo) {
    return JSON.stringify(exports.ConversationUsageInfo$outboundSchema.parse(conversationUsageInfo));
}
function conversationUsageInfoFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ConversationUsageInfo$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ConversationUsageInfo' from JSON`);
}
//# sourceMappingURL=conversationusageinfo.js.map