"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MetricOut$ = exports.MetricOut$outboundSchema = exports.MetricOut$inboundSchema = void 0;
exports.metricOutToJSON = metricOutToJSON;
exports.metricOutFromJSON = metricOutFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.MetricOut$inboundSchema = z.object({
    train_loss: z.nullable(z.number()).optional(),
    valid_loss: z.nullable(z.number()).optional(),
    valid_mean_token_accuracy: z.nullable(z.number()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "train_loss": "trainLoss",
        "valid_loss": "validLoss",
        "valid_mean_token_accuracy": "validMeanTokenAccuracy",
    });
});
/** @internal */
exports.MetricOut$outboundSchema = z.object({
    trainLoss: z.nullable(z.number()).optional(),
    validLoss: z.nullable(z.number()).optional(),
    validMeanTokenAccuracy: z.nullable(z.number()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        trainLoss: "train_loss",
        validLoss: "valid_loss",
        validMeanTokenAccuracy: "valid_mean_token_accuracy",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MetricOut$;
(function (MetricOut$) {
    /** @deprecated use `MetricOut$inboundSchema` instead. */
    MetricOut$.inboundSchema = exports.MetricOut$inboundSchema;
    /** @deprecated use `MetricOut$outboundSchema` instead. */
    MetricOut$.outboundSchema = exports.MetricOut$outboundSchema;
})(MetricOut$ || (exports.MetricOut$ = MetricOut$ = {}));
function metricOutToJSON(metricOut) {
    return JSON.stringify(exports.MetricOut$outboundSchema.parse(metricOut));
}
function metricOutFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.MetricOut$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'MetricOut' from JSON`);
}
//# sourceMappingURL=metricout.js.map