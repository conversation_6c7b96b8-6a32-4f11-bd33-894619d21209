"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationRestartRequest$ = exports.ConversationRestartRequest$outboundSchema = exports.ConversationRestartRequest$inboundSchema = exports.ConversationRestartRequestHandoffExecution$ = exports.ConversationRestartRequestHandoffExecution$outboundSchema = exports.ConversationRestartRequestHandoffExecution$inboundSchema = exports.ConversationRestartRequestHandoffExecution = void 0;
exports.conversationRestartRequestToJSON = conversationRestartRequestToJSON;
exports.conversationRestartRequestFromJSON = conversationRestartRequestFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const completionargs_js_1 = require("./completionargs.js");
const conversationinputs_js_1 = require("./conversationinputs.js");
exports.ConversationRestartRequestHandoffExecution = {
    Client: "client",
    Server: "server",
};
/** @internal */
exports.ConversationRestartRequestHandoffExecution$inboundSchema = z
    .nativeEnum(exports.ConversationRestartRequestHandoffExecution);
/** @internal */
exports.ConversationRestartRequestHandoffExecution$outboundSchema = exports.ConversationRestartRequestHandoffExecution$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationRestartRequestHandoffExecution$;
(function (ConversationRestartRequestHandoffExecution$) {
    /** @deprecated use `ConversationRestartRequestHandoffExecution$inboundSchema` instead. */
    ConversationRestartRequestHandoffExecution$.inboundSchema = exports.ConversationRestartRequestHandoffExecution$inboundSchema;
    /** @deprecated use `ConversationRestartRequestHandoffExecution$outboundSchema` instead. */
    ConversationRestartRequestHandoffExecution$.outboundSchema = exports.ConversationRestartRequestHandoffExecution$outboundSchema;
})(ConversationRestartRequestHandoffExecution$ || (exports.ConversationRestartRequestHandoffExecution$ = ConversationRestartRequestHandoffExecution$ = {}));
/** @internal */
exports.ConversationRestartRequest$inboundSchema = z.object({
    inputs: conversationinputs_js_1.ConversationInputs$inboundSchema,
    stream: z.boolean().default(false),
    store: z.boolean().default(true),
    handoff_execution: exports.ConversationRestartRequestHandoffExecution$inboundSchema
        .default("server"),
    from_entry_id: z.string(),
    completion_args: completionargs_js_1.CompletionArgs$inboundSchema.optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "handoff_execution": "handoffExecution",
        "from_entry_id": "fromEntryId",
        "completion_args": "completionArgs",
    });
});
/** @internal */
exports.ConversationRestartRequest$outboundSchema = z.object({
    inputs: conversationinputs_js_1.ConversationInputs$outboundSchema,
    stream: z.boolean().default(false),
    store: z.boolean().default(true),
    handoffExecution: exports.ConversationRestartRequestHandoffExecution$outboundSchema
        .default("server"),
    fromEntryId: z.string(),
    completionArgs: completionargs_js_1.CompletionArgs$outboundSchema.optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        handoffExecution: "handoff_execution",
        fromEntryId: "from_entry_id",
        completionArgs: "completion_args",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationRestartRequest$;
(function (ConversationRestartRequest$) {
    /** @deprecated use `ConversationRestartRequest$inboundSchema` instead. */
    ConversationRestartRequest$.inboundSchema = exports.ConversationRestartRequest$inboundSchema;
    /** @deprecated use `ConversationRestartRequest$outboundSchema` instead. */
    ConversationRestartRequest$.outboundSchema = exports.ConversationRestartRequest$outboundSchema;
})(ConversationRestartRequest$ || (exports.ConversationRestartRequest$ = ConversationRestartRequest$ = {}));
function conversationRestartRequestToJSON(conversationRestartRequest) {
    return JSON.stringify(exports.ConversationRestartRequest$outboundSchema.parse(conversationRestartRequest));
}
function conversationRestartRequestFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ConversationRestartRequest$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ConversationRestartRequest' from JSON`);
}
//# sourceMappingURL=conversationrestartrequest.js.map