"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityError = exports.SecurityErrorCode = void 0;
exports.resolveSecurity = resolveSecurity;
exports.resolveGlobalSecurity = resolveGlobalSecurity;
exports.extractSecurity = extractSecurity;
const env_js_1 = require("./env.js");
var SecurityErrorCode;
(function (SecurityErrorCode) {
    SecurityErrorCode["Incomplete"] = "incomplete";
    SecurityErrorCode["UnrecognisedSecurityType"] = "unrecognized_security_type";
})(SecurityErrorCode || (exports.SecurityErrorCode = SecurityErrorCode = {}));
class SecurityError extends Error {
    constructor(code, message) {
        super(message);
        this.code = code;
        this.name = "SecurityError";
    }
    static incomplete() {
        return new SecurityError(SecurityErrorCode.Incomplete, "Security requirements not met in order to perform the operation");
    }
    static unrecognizedType(type) {
        return new SecurityError(SecurityErrorCode.UnrecognisedSecurityType, `Unrecognised security type: ${type}`);
    }
}
exports.SecurityError = SecurityError;
function resolveSecurity(...options) {
    const state = {
        basic: {},
        headers: {},
        queryParams: {},
        cookies: {},
        oauth2: { type: "none" },
    };
    const option = options.find((opts) => {
        return opts.every((o) => {
            if (o.value == null) {
                return false;
            }
            else if (o.type === "http:basic") {
                return o.value.username != null || o.value.password != null;
            }
            else if (o.type === "http:custom") {
                return null;
            }
            else if (o.type === "oauth2:password") {
                return (typeof o.value === "string" && !!o.value);
            }
            else if (o.type === "oauth2:client_credentials") {
                return o.value.clientID != null || o.value.clientSecret != null;
            }
            else if (typeof o.value === "string") {
                return !!o.value;
            }
            else {
                throw new Error(`Unrecognized security type: ${o.type} (value type: ${typeof o
                    .value})`);
            }
        });
    });
    if (option == null) {
        return null;
    }
    option.forEach((spec) => {
        if (spec.value == null) {
            return;
        }
        const { type } = spec;
        switch (type) {
            case "apiKey:header":
                state.headers[spec.fieldName] = spec.value;
                break;
            case "apiKey:query":
                state.queryParams[spec.fieldName] = spec.value;
                break;
            case "apiKey:cookie":
                state.cookies[spec.fieldName] = spec.value;
                break;
            case "http:basic":
                applyBasic(state, spec);
                break;
            case "http:custom":
                break;
            case "http:bearer":
                applyBearer(state, spec);
                break;
            case "oauth2":
                applyBearer(state, spec);
                break;
            case "oauth2:password":
                applyBearer(state, spec);
                break;
            case "oauth2:client_credentials":
                break;
            case "openIdConnect":
                applyBearer(state, spec);
                break;
            default:
                spec;
                throw SecurityError.unrecognizedType(type);
        }
    });
    return state;
}
function applyBasic(state, spec) {
    if (spec.value == null) {
        return;
    }
    state.basic = spec.value;
}
function applyBearer(state, spec) {
    if (typeof spec.value !== "string" || !spec.value) {
        return;
    }
    let value = spec.value;
    if (value.slice(0, 7).toLowerCase() !== "bearer ") {
        value = `Bearer ${value}`;
    }
    state.headers[spec.fieldName] = value;
}
function resolveGlobalSecurity(security) {
    return resolveSecurity([
        {
            fieldName: "Authorization",
            type: "http:bearer",
            value: security?.apiKey ?? (0, env_js_1.env)().MISTRAL_API_KEY,
        },
    ]);
}
async function extractSecurity(sec) {
    if (sec == null) {
        return;
    }
    return typeof sec === "function" ? sec() : sec;
}
//# sourceMappingURL=security.js.map