{"version": 3, "file": "conversationhistory.d.ts", "sourceRoot": "", "sources": ["../../src/models/components/conversationhistory.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC;AAGzB,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAClD,OAAO,EAAE,MAAM,IAAI,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAC9D,OAAO,EAAE,kBAAkB,EAAE,MAAM,iCAAiC,CAAC;AACrE,OAAO,EACL,iBAAiB,EAEjB,0BAA0B,EAE3B,MAAM,wBAAwB,CAAC;AAChC,OAAO,EACL,iBAAiB,EAEjB,0BAA0B,EAE3B,MAAM,wBAAwB,CAAC;AAChC,OAAO,EACL,mBAAmB,EAEnB,4BAA4B,EAE7B,MAAM,0BAA0B,CAAC;AAClC,OAAO,EACL,iBAAiB,EAEjB,0BAA0B,EAE3B,MAAM,wBAAwB,CAAC;AAChC,OAAO,EACL,kBAAkB,EAElB,2BAA2B,EAE5B,MAAM,yBAAyB,CAAC;AACjC,OAAO,EACL,kBAAkB,EAElB,2BAA2B,EAE5B,MAAM,yBAAyB,CAAC;AAEjC,eAAO,MAAM,yBAAyB;;CAE5B,CAAC;AACX,MAAM,MAAM,yBAAyB,GAAG,UAAU,CAChD,OAAO,yBAAyB,CACjC,CAAC;AAEF,MAAM,MAAM,OAAO,GACf,iBAAiB,GACjB,mBAAmB,GACnB,kBAAkB,GAClB,iBAAiB,GACjB,kBAAkB,GAClB,iBAAiB,CAAC;AAEtB;;GAEG;AACH,MAAM,MAAM,mBAAmB,GAAG;IAChC,MAAM,CAAC,EAAE,yBAAyB,GAAG,SAAS,CAAC;IAC/C,cAAc,EAAE,MAAM,CAAC;IACvB,OAAO,EAAE,KAAK,CACV,iBAAiB,GACjB,mBAAmB,GACnB,kBAAkB,GAClB,iBAAiB,GACjB,kBAAkB,GAClB,iBAAiB,CACpB,CAAC;CACH,CAAC;AAEF,gBAAgB;AAChB,eAAO,MAAM,uCAAuC,EAAE,CAAC,CAAC,aAAa,CACnE,OAAO,yBAAyB,CACS,CAAC;AAE5C,gBAAgB;AAChB,eAAO,MAAM,wCAAwC,EAAE,CAAC,CAAC,aAAa,CACpE,OAAO,yBAAyB,CACS,CAAC;AAE5C;;;GAGG;AACH,yBAAiB,0BAA0B,CAAC;IAC1C,yEAAyE;IAClE,MAAM,aAAa;;MAA0C,CAAC;IACrE,0EAA0E;IACnE,MAAM,cAAc;;MAA2C,CAAC;CACxE;AAED,gBAAgB;AAChB,eAAO,MAAM,qBAAqB,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,UAAU,EAAE,OAAO,CAQxE,CAAC;AAEL,gBAAgB;AAChB,MAAM,MAAM,gBAAgB,GACxB,0BAA0B,GAC1B,4BAA4B,GAC5B,2BAA2B,GAC3B,0BAA0B,GAC1B,2BAA2B,GAC3B,0BAA0B,CAAC;AAE/B,gBAAgB;AAChB,eAAO,MAAM,sBAAsB,EAAE,CAAC,CAAC,OAAO,CAC5C,gBAAgB,EAChB,CAAC,CAAC,UAAU,EACZ,OAAO,CAQP,CAAC;AAEH;;;GAGG;AACH,yBAAiB,QAAQ,CAAC;IACxB,uDAAuD;IAChD,MAAM,aAAa,2CAAwB,CAAC;IACnD,wDAAwD;IACjD,MAAM,cAAc,oDAAyB,CAAC;IACrD,kDAAkD;IAClD,KAAY,QAAQ,GAAG,gBAAgB,CAAC;CACzC;AAED,wBAAgB,aAAa,CAAC,OAAO,EAAE,OAAO,GAAG,MAAM,CAEtD;AAED,wBAAgB,eAAe,CAC7B,UAAU,EAAE,MAAM,GACjB,eAAe,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAM9C;AAED,gBAAgB;AAChB,eAAO,MAAM,iCAAiC,EAAE,CAAC,CAAC,OAAO,CACvD,mBAAmB,EACnB,CAAC,CAAC,UAAU,EACZ,OAAO,CAoBP,CAAC;AAEH,gBAAgB;AAChB,MAAM,MAAM,4BAA4B,GAAG;IACzC,MAAM,EAAE,MAAM,CAAC;IACf,eAAe,EAAE,MAAM,CAAC;IACxB,OAAO,EAAE,KAAK,CACV,0BAA0B,GAC1B,4BAA4B,GAC5B,2BAA2B,GAC3B,0BAA0B,GAC1B,2BAA2B,GAC3B,0BAA0B,CAC7B,CAAC;CACH,CAAC;AAEF,gBAAgB;AAChB,eAAO,MAAM,kCAAkC,EAAE,CAAC,CAAC,OAAO,CACxD,4BAA4B,EAC5B,CAAC,CAAC,UAAU,EACZ,mBAAmB,CAoBnB,CAAC;AAEH;;;GAGG;AACH,yBAAiB,oBAAoB,CAAC;IACpC,mEAAmE;IAC5D,MAAM,aAAa,uDAAoC,CAAC;IAC/D,oEAAoE;IAC7D,MAAM,cAAc,4EAAqC,CAAC;IACjE,8DAA8D;IAC9D,KAAY,QAAQ,GAAG,4BAA4B,CAAC;CACrD;AAED,wBAAgB,yBAAyB,CACvC,mBAAmB,EAAE,mBAAmB,GACvC,MAAM,CAIR;AAED,wBAAgB,2BAA2B,CACzC,UAAU,EAAE,MAAM,GACjB,eAAe,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,CAM1D"}