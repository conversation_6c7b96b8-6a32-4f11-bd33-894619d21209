"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationHistory$ = exports.ConversationHistory$outboundSchema = exports.ConversationHistory$inboundSchema = exports.Entries$ = exports.Entries$outboundSchema = exports.Entries$inboundSchema = exports.ConversationHistoryObject$ = exports.ConversationHistoryObject$outboundSchema = exports.ConversationHistoryObject$inboundSchema = exports.ConversationHistoryObject = void 0;
exports.entriesToJSON = entriesToJSON;
exports.entriesFromJSON = entriesFromJSON;
exports.conversationHistoryToJSON = conversationHistoryToJSON;
exports.conversationHistoryFromJSON = conversationHistoryFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const agenthandoffentry_js_1 = require("./agenthandoffentry.js");
const functioncallentry_js_1 = require("./functioncallentry.js");
const functionresultentry_js_1 = require("./functionresultentry.js");
const messageinputentry_js_1 = require("./messageinputentry.js");
const messageoutputentry_js_1 = require("./messageoutputentry.js");
const toolexecutionentry_js_1 = require("./toolexecutionentry.js");
exports.ConversationHistoryObject = {
    ConversationHistory: "conversation.history",
};
/** @internal */
exports.ConversationHistoryObject$inboundSchema = z.nativeEnum(exports.ConversationHistoryObject);
/** @internal */
exports.ConversationHistoryObject$outboundSchema = exports.ConversationHistoryObject$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationHistoryObject$;
(function (ConversationHistoryObject$) {
    /** @deprecated use `ConversationHistoryObject$inboundSchema` instead. */
    ConversationHistoryObject$.inboundSchema = exports.ConversationHistoryObject$inboundSchema;
    /** @deprecated use `ConversationHistoryObject$outboundSchema` instead. */
    ConversationHistoryObject$.outboundSchema = exports.ConversationHistoryObject$outboundSchema;
})(ConversationHistoryObject$ || (exports.ConversationHistoryObject$ = ConversationHistoryObject$ = {}));
/** @internal */
exports.Entries$inboundSchema = z.union([
    messageinputentry_js_1.MessageInputEntry$inboundSchema,
    functionresultentry_js_1.FunctionResultEntry$inboundSchema,
    toolexecutionentry_js_1.ToolExecutionEntry$inboundSchema,
    functioncallentry_js_1.FunctionCallEntry$inboundSchema,
    messageoutputentry_js_1.MessageOutputEntry$inboundSchema,
    agenthandoffentry_js_1.AgentHandoffEntry$inboundSchema,
]);
/** @internal */
exports.Entries$outboundSchema = z.union([
    messageinputentry_js_1.MessageInputEntry$outboundSchema,
    functionresultentry_js_1.FunctionResultEntry$outboundSchema,
    toolexecutionentry_js_1.ToolExecutionEntry$outboundSchema,
    functioncallentry_js_1.FunctionCallEntry$outboundSchema,
    messageoutputentry_js_1.MessageOutputEntry$outboundSchema,
    agenthandoffentry_js_1.AgentHandoffEntry$outboundSchema,
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Entries$;
(function (Entries$) {
    /** @deprecated use `Entries$inboundSchema` instead. */
    Entries$.inboundSchema = exports.Entries$inboundSchema;
    /** @deprecated use `Entries$outboundSchema` instead. */
    Entries$.outboundSchema = exports.Entries$outboundSchema;
})(Entries$ || (exports.Entries$ = Entries$ = {}));
function entriesToJSON(entries) {
    return JSON.stringify(exports.Entries$outboundSchema.parse(entries));
}
function entriesFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.Entries$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'Entries' from JSON`);
}
/** @internal */
exports.ConversationHistory$inboundSchema = z.object({
    object: exports.ConversationHistoryObject$inboundSchema.default("conversation.history"),
    conversation_id: z.string(),
    entries: z.array(z.union([
        messageinputentry_js_1.MessageInputEntry$inboundSchema,
        functionresultentry_js_1.FunctionResultEntry$inboundSchema,
        toolexecutionentry_js_1.ToolExecutionEntry$inboundSchema,
        functioncallentry_js_1.FunctionCallEntry$inboundSchema,
        messageoutputentry_js_1.MessageOutputEntry$inboundSchema,
        agenthandoffentry_js_1.AgentHandoffEntry$inboundSchema,
    ])),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "conversation_id": "conversationId",
    });
});
/** @internal */
exports.ConversationHistory$outboundSchema = z.object({
    object: exports.ConversationHistoryObject$outboundSchema.default("conversation.history"),
    conversationId: z.string(),
    entries: z.array(z.union([
        messageinputentry_js_1.MessageInputEntry$outboundSchema,
        functionresultentry_js_1.FunctionResultEntry$outboundSchema,
        toolexecutionentry_js_1.ToolExecutionEntry$outboundSchema,
        functioncallentry_js_1.FunctionCallEntry$outboundSchema,
        messageoutputentry_js_1.MessageOutputEntry$outboundSchema,
        agenthandoffentry_js_1.AgentHandoffEntry$outboundSchema,
    ])),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        conversationId: "conversation_id",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationHistory$;
(function (ConversationHistory$) {
    /** @deprecated use `ConversationHistory$inboundSchema` instead. */
    ConversationHistory$.inboundSchema = exports.ConversationHistory$inboundSchema;
    /** @deprecated use `ConversationHistory$outboundSchema` instead. */
    ConversationHistory$.outboundSchema = exports.ConversationHistory$outboundSchema;
})(ConversationHistory$ || (exports.ConversationHistory$ = ConversationHistory$ = {}));
function conversationHistoryToJSON(conversationHistory) {
    return JSON.stringify(exports.ConversationHistory$outboundSchema.parse(conversationHistory));
}
function conversationHistoryFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ConversationHistory$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ConversationHistory' from JSON`);
}
//# sourceMappingURL=conversationhistory.js.map