"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationEvents$ = exports.ConversationEvents$outboundSchema = exports.ConversationEvents$inboundSchema = exports.ConversationEventsData$ = exports.ConversationEventsData$outboundSchema = exports.ConversationEventsData$inboundSchema = void 0;
exports.conversationEventsDataToJSON = conversationEventsDataToJSON;
exports.conversationEventsDataFromJSON = conversationEventsDataFromJSON;
exports.conversationEventsToJSON = conversationEventsToJSON;
exports.conversationEventsFromJSON = conversationEventsFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const agenthandoffdoneevent_js_1 = require("./agenthandoffdoneevent.js");
const agenthandoffstartedevent_js_1 = require("./agenthandoffstartedevent.js");
const functioncallevent_js_1 = require("./functioncallevent.js");
const messageoutputevent_js_1 = require("./messageoutputevent.js");
const responsedoneevent_js_1 = require("./responsedoneevent.js");
const responseerrorevent_js_1 = require("./responseerrorevent.js");
const responsestartedevent_js_1 = require("./responsestartedevent.js");
const ssetypes_js_1 = require("./ssetypes.js");
const toolexecutiondoneevent_js_1 = require("./toolexecutiondoneevent.js");
const toolexecutionstartedevent_js_1 = require("./toolexecutionstartedevent.js");
/** @internal */
exports.ConversationEventsData$inboundSchema = z.union([
    responsedoneevent_js_1.ResponseDoneEvent$inboundSchema.and(z.object({ type: z.literal("conversation.response.done") }).transform((v) => ({ type: v.type }))),
    responsestartedevent_js_1.ResponseStartedEvent$inboundSchema.and(z.object({ type: z.literal("conversation.response.started") }).transform((v) => ({ type: v.type }))),
    responseerrorevent_js_1.ResponseErrorEvent$inboundSchema.and(z.object({ type: z.literal("conversation.response.error") }).transform((v) => ({ type: v.type }))),
    toolexecutionstartedevent_js_1.ToolExecutionStartedEvent$inboundSchema.and(z.object({ type: z.literal("tool.execution.started") }).transform((v) => ({
        type: v.type,
    }))),
    agenthandoffdoneevent_js_1.AgentHandoffDoneEvent$inboundSchema.and(z.object({ type: z.literal("agent.handoff.done") }).transform((v) => ({
        type: v.type,
    }))),
    agenthandoffstartedevent_js_1.AgentHandoffStartedEvent$inboundSchema.and(z.object({ type: z.literal("agent.handoff.started") }).transform((v) => ({
        type: v.type,
    }))),
    toolexecutiondoneevent_js_1.ToolExecutionDoneEvent$inboundSchema.and(z.object({ type: z.literal("tool.execution.done") }).transform((v) => ({
        type: v.type,
    }))),
    functioncallevent_js_1.FunctionCallEvent$inboundSchema.and(z.object({ type: z.literal("function.call.delta") }).transform((v) => ({
        type: v.type,
    }))),
    messageoutputevent_js_1.MessageOutputEvent$inboundSchema.and(z.object({ type: z.literal("message.output.delta") }).transform((v) => ({
        type: v.type,
    }))),
]);
/** @internal */
exports.ConversationEventsData$outboundSchema = z.union([
    responsedoneevent_js_1.ResponseDoneEvent$outboundSchema.and(z.object({ type: z.literal("conversation.response.done") }).transform((v) => ({ type: v.type }))),
    responsestartedevent_js_1.ResponseStartedEvent$outboundSchema.and(z.object({ type: z.literal("conversation.response.started") }).transform((v) => ({ type: v.type }))),
    responseerrorevent_js_1.ResponseErrorEvent$outboundSchema.and(z.object({ type: z.literal("conversation.response.error") }).transform((v) => ({ type: v.type }))),
    toolexecutionstartedevent_js_1.ToolExecutionStartedEvent$outboundSchema.and(z.object({ type: z.literal("tool.execution.started") }).transform((v) => ({
        type: v.type,
    }))),
    agenthandoffdoneevent_js_1.AgentHandoffDoneEvent$outboundSchema.and(z.object({ type: z.literal("agent.handoff.done") }).transform((v) => ({
        type: v.type,
    }))),
    agenthandoffstartedevent_js_1.AgentHandoffStartedEvent$outboundSchema.and(z.object({ type: z.literal("agent.handoff.started") }).transform((v) => ({
        type: v.type,
    }))),
    toolexecutiondoneevent_js_1.ToolExecutionDoneEvent$outboundSchema.and(z.object({ type: z.literal("tool.execution.done") }).transform((v) => ({
        type: v.type,
    }))),
    functioncallevent_js_1.FunctionCallEvent$outboundSchema.and(z.object({ type: z.literal("function.call.delta") }).transform((v) => ({
        type: v.type,
    }))),
    messageoutputevent_js_1.MessageOutputEvent$outboundSchema.and(z.object({ type: z.literal("message.output.delta") }).transform((v) => ({
        type: v.type,
    }))),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationEventsData$;
(function (ConversationEventsData$) {
    /** @deprecated use `ConversationEventsData$inboundSchema` instead. */
    ConversationEventsData$.inboundSchema = exports.ConversationEventsData$inboundSchema;
    /** @deprecated use `ConversationEventsData$outboundSchema` instead. */
    ConversationEventsData$.outboundSchema = exports.ConversationEventsData$outboundSchema;
})(ConversationEventsData$ || (exports.ConversationEventsData$ = ConversationEventsData$ = {}));
function conversationEventsDataToJSON(conversationEventsData) {
    return JSON.stringify(exports.ConversationEventsData$outboundSchema.parse(conversationEventsData));
}
function conversationEventsDataFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ConversationEventsData$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ConversationEventsData' from JSON`);
}
/** @internal */
exports.ConversationEvents$inboundSchema = z.object({
    event: ssetypes_js_1.SSETypes$inboundSchema,
    data: z.string().transform((v, ctx) => {
        try {
            return JSON.parse(v);
        }
        catch (err) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: `malformed json: ${err}`,
            });
            return z.NEVER;
        }
    }).pipe(z.union([
        responsedoneevent_js_1.ResponseDoneEvent$inboundSchema.and(z.object({ type: z.literal("conversation.response.done") }).transform((v) => ({ type: v.type }))),
        responsestartedevent_js_1.ResponseStartedEvent$inboundSchema.and(z.object({ type: z.literal("conversation.response.started") })
            .transform((v) => ({ type: v.type }))),
        responseerrorevent_js_1.ResponseErrorEvent$inboundSchema.and(z.object({ type: z.literal("conversation.response.error") }).transform((v) => ({ type: v.type }))),
        toolexecutionstartedevent_js_1.ToolExecutionStartedEvent$inboundSchema.and(z.object({ type: z.literal("tool.execution.started") }).transform((v) => ({ type: v.type }))),
        agenthandoffdoneevent_js_1.AgentHandoffDoneEvent$inboundSchema.and(z.object({ type: z.literal("agent.handoff.done") }).transform((v) => ({
            type: v.type,
        }))),
        agenthandoffstartedevent_js_1.AgentHandoffStartedEvent$inboundSchema.and(z.object({ type: z.literal("agent.handoff.started") }).transform((v) => ({ type: v.type }))),
        toolexecutiondoneevent_js_1.ToolExecutionDoneEvent$inboundSchema.and(z.object({ type: z.literal("tool.execution.done") }).transform((v) => ({
            type: v.type,
        }))),
        functioncallevent_js_1.FunctionCallEvent$inboundSchema.and(z.object({ type: z.literal("function.call.delta") }).transform((v) => ({
            type: v.type,
        }))),
        messageoutputevent_js_1.MessageOutputEvent$inboundSchema.and(z.object({ type: z.literal("message.output.delta") }).transform((v) => ({ type: v.type }))),
    ])),
});
/** @internal */
exports.ConversationEvents$outboundSchema = z.object({
    event: ssetypes_js_1.SSETypes$outboundSchema,
    data: z.union([
        responsedoneevent_js_1.ResponseDoneEvent$outboundSchema.and(z.object({ type: z.literal("conversation.response.done") }).transform((v) => ({ type: v.type }))),
        responsestartedevent_js_1.ResponseStartedEvent$outboundSchema.and(z.object({ type: z.literal("conversation.response.started") }).transform((v) => ({ type: v.type }))),
        responseerrorevent_js_1.ResponseErrorEvent$outboundSchema.and(z.object({ type: z.literal("conversation.response.error") }).transform((v) => ({ type: v.type }))),
        toolexecutionstartedevent_js_1.ToolExecutionStartedEvent$outboundSchema.and(z.object({ type: z.literal("tool.execution.started") }).transform((v) => ({ type: v.type }))),
        agenthandoffdoneevent_js_1.AgentHandoffDoneEvent$outboundSchema.and(z.object({ type: z.literal("agent.handoff.done") }).transform((v) => ({
            type: v.type,
        }))),
        agenthandoffstartedevent_js_1.AgentHandoffStartedEvent$outboundSchema.and(z.object({ type: z.literal("agent.handoff.started") }).transform((v) => ({
            type: v.type,
        }))),
        toolexecutiondoneevent_js_1.ToolExecutionDoneEvent$outboundSchema.and(z.object({ type: z.literal("tool.execution.done") }).transform((v) => ({
            type: v.type,
        }))),
        functioncallevent_js_1.FunctionCallEvent$outboundSchema.and(z.object({ type: z.literal("function.call.delta") }).transform((v) => ({
            type: v.type,
        }))),
        messageoutputevent_js_1.MessageOutputEvent$outboundSchema.and(z.object({ type: z.literal("message.output.delta") }).transform((v) => ({
            type: v.type,
        }))),
    ]),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationEvents$;
(function (ConversationEvents$) {
    /** @deprecated use `ConversationEvents$inboundSchema` instead. */
    ConversationEvents$.inboundSchema = exports.ConversationEvents$inboundSchema;
    /** @deprecated use `ConversationEvents$outboundSchema` instead. */
    ConversationEvents$.outboundSchema = exports.ConversationEvents$outboundSchema;
})(ConversationEvents$ || (exports.ConversationEvents$ = ConversationEvents$ = {}));
function conversationEventsToJSON(conversationEvents) {
    return JSON.stringify(exports.ConversationEvents$outboundSchema.parse(conversationEvents));
}
function conversationEventsFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ConversationEvents$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ConversationEvents' from JSON`);
}
//# sourceMappingURL=conversationevents.js.map