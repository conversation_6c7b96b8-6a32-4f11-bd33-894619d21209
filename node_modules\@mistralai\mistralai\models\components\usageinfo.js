"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsageInfo$ = exports.UsageInfo$outboundSchema = exports.UsageInfo$inboundSchema = void 0;
exports.usageInfoToJSON = usageInfoToJSON;
exports.usageInfoFromJSON = usageInfoFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.UsageInfo$inboundSchema = z.object({
    prompt_tokens: z.number().int(),
    completion_tokens: z.number().int(),
    total_tokens: z.number().int(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "prompt_tokens": "promptTokens",
        "completion_tokens": "completionTokens",
        "total_tokens": "totalTokens",
    });
});
/** @internal */
exports.UsageInfo$outboundSchema = z.object({
    promptTokens: z.number().int(),
    completionTokens: z.number().int(),
    totalTokens: z.number().int(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        promptTokens: "prompt_tokens",
        completionTokens: "completion_tokens",
        totalTokens: "total_tokens",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var UsageInfo$;
(function (UsageInfo$) {
    /** @deprecated use `UsageInfo$inboundSchema` instead. */
    UsageInfo$.inboundSchema = exports.UsageInfo$inboundSchema;
    /** @deprecated use `UsageInfo$outboundSchema` instead. */
    UsageInfo$.outboundSchema = exports.UsageInfo$outboundSchema;
})(UsageInfo$ || (exports.UsageInfo$ = UsageInfo$ = {}));
function usageInfoToJSON(usageInfo) {
    return JSON.stringify(exports.UsageInfo$outboundSchema.parse(usageInfo));
}
function usageInfoFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.UsageInfo$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'UsageInfo' from JSON`);
}
//# sourceMappingURL=usageinfo.js.map