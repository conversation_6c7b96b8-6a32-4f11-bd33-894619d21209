{"version": 3, "file": "chatcompletionrequest.js", "sourceRoot": "", "sources": ["../../src/models/components/chatcompletionrequest.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;AAqKH,gCAEC;AAED,oCAQC;AAsED,wCAEC;AAED,4CAQC;AAkCD,sFAQC;AAED,0FAQC;AA0KD,kEAMC;AAED,sEAQC;AA/eD,uCAAyB;AACzB,2DAA0D;AAC1D,qDAAiD;AAGjD,+DAK+B;AAC/B,iEAIgC;AAChC,mDAKyB;AACzB,2DAK6B;AAC7B,yDAK4B;AAC5B,uCAKmB;AACnB,mDAKyB;AACzB,2DAI6B;AAC7B,qDAK0B;AAC1B,qDAK0B;AA6E1B,gBAAgB;AACH,QAAA,kBAAkB,GAA2C,CAAC;KACxE,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;AAK5C,gBAAgB;AACH,QAAA,mBAAmB,GAC9B,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;AAE7C;;;GAGG;AACH,IAAiB,KAAK,CAOrB;AAPD,WAAiB,KAAK;IACpB,oDAAoD;IACvC,mBAAa,GAAG,0BAAkB,CAAC;IAChD,qDAAqD;IACxC,oBAAc,GAAG,2BAAmB,CAAC;AAGpD,CAAC,EAPgB,KAAK,qBAAL,KAAK,QAOrB;AAED,SAAgB,UAAU,CAAC,IAAU;IACnC,OAAO,IAAI,CAAC,SAAS,CAAC,2BAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AACzD,CAAC;AAED,SAAgB,YAAY,CAC1B,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,0BAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC9C,kCAAkC,CACnC,CAAC;AACJ,CAAC;AAED,gBAAgB;AACH,QAAA,sBAAsB,GAI/B,CAAC,CAAC,KAAK,CAAC;IACV,8CAA2B,CAAC,GAAG,CAC7B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACxD,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,0CAAyB,CAAC,GAAG,CAC3B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAC3E;IACD,oDAA8B,CAAC,GAAG,CAChC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,0CAAyB,CAAC,GAAG,CAC3B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAC3E;CACF,CAAC,CAAC;AASH,gBAAgB;AACH,QAAA,uBAAuB,GAIhC,CAAC,CAAC,KAAK,CAAC;IACV,+CAA4B,CAAC,GAAG,CAC9B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACxD,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,2CAA0B,CAAC,GAAG,CAC5B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAC3E;IACD,qDAA+B,CAAC,GAAG,CACjC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,2CAA0B,CAAC,GAAG,CAC5B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAC3E;CACF,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,SAAS,CAOzB;AAPD,WAAiB,SAAS;IACxB,wDAAwD;IAC3C,uBAAa,GAAG,8BAAsB,CAAC;IACpD,yDAAyD;IAC5C,wBAAc,GAAG,+BAAuB,CAAC;AAGxD,CAAC,EAPgB,SAAS,yBAAT,SAAS,QAOzB;AAED,SAAgB,cAAc,CAAC,QAAkB;IAC/C,OAAO,IAAI,CAAC,SAAS,CAAC,+BAAuB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;AACjE,CAAC;AAED,SAAgB,gBAAgB,CAC9B,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,8BAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAClD,sCAAsC,CACvC,CAAC;AACJ,CAAC;AAED,gBAAgB;AACH,QAAA,6CAA6C,GAItD,CAAC,CAAC,KAAK,CAAC,CAAC,wCAAwB,EAAE,gDAA4B,CAAC,CAAC,CAAC;AAOtE,gBAAgB;AACH,QAAA,8CAA8C,GAIvD,CAAC,CAAC,KAAK,CAAC,CAAC,yCAAyB,EAAE,iDAA6B,CAAC,CAAC,CAAC;AAExE;;;GAGG;AACH,IAAiB,gCAAgC,CAOhD;AAPD,WAAiB,gCAAgC;IAC/C,+EAA+E;IAClE,8CAAa,GAAG,qDAA6C,CAAC;IAC3E,gFAAgF;IACnE,+CAAc,GAAG,sDAA8C,CAAC;AAG/E,CAAC,EAPgB,gCAAgC,gDAAhC,gCAAgC,QAOhD;AAED,SAAgB,qCAAqC,CACnD,+BAAgE;IAEhE,OAAO,IAAI,CAAC,SAAS,CACnB,sDAA8C,CAAC,KAAK,CAClD,+BAA+B,CAChC,CACF,CAAC;AACJ,CAAC;AAED,SAAgB,uCAAuC,CACrD,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,qDAA6C,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACzE,6DAA6D,CAC9D,CAAC;AACJ,CAAC;AAED,gBAAgB;AACH,QAAA,mCAAmC,GAI5C,CAAC,CAAC,MAAM,CAAC;IACX,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE;IACjB,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC9C,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IACnD,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAClC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC3D,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IACpD,QAAQ,EAAE,CAAC,CAAC,KAAK,CACf,CAAC,CAAC,KAAK,CAAC;QACN,8CAA2B,CAAC,GAAG,CAC7B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACxD,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,0CAAyB,CAAC,GAAG,CAC3B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACtD,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,oDAA8B,CAAC,GAAG,CAChC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC3D,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,0CAAyB,CAAC,GAAG,CAC3B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACtD,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;KACF,CAAC,CACH;IACD,eAAe,EAAE,gDAA4B,CAAC,QAAQ,EAAE;IACxD,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,4BAAkB,CAAC,CAAC,CAAC,QAAQ,EAAE;IACzD,WAAW,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,wCAAwB,EAAE,gDAA4B,CAAC,CAAC;SAC3E,QAAQ,EAAE;IACb,gBAAgB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACvC,iBAAiB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACxC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC1C,UAAU,EAAE,wCAAwB,CAAC,QAAQ,EAAE;IAC/C,mBAAmB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;IAC3C,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,sDAA+B,CAAC,CAAC,QAAQ,EAAE;IACnE,WAAW,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;CACpC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,OAAO,EAAE,MAAM;QACf,YAAY,EAAE,WAAW;QACzB,aAAa,EAAE,YAAY;QAC3B,iBAAiB,EAAE,gBAAgB;QACnC,aAAa,EAAE,YAAY;QAC3B,kBAAkB,EAAE,iBAAiB;QACrC,mBAAmB,EAAE,kBAAkB;QACvC,qBAAqB,EAAE,mBAAmB;QAC1C,aAAa,EAAE,YAAY;QAC3B,aAAa,EAAE,YAAY;KAC5B,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AA6BH,gBAAgB;AACH,QAAA,oCAAoC,GAI7C,CAAC,CAAC,MAAM,CAAC;IACX,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE;IACjB,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC9C,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC3B,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IAClD,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAClC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC3D,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IACnD,QAAQ,EAAE,CAAC,CAAC,KAAK,CACf,CAAC,CAAC,KAAK,CAAC;QACN,+CAA4B,CAAC,GAAG,CAC9B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACxD,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,2CAA0B,CAAC,GAAG,CAC5B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACtD,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,qDAA+B,CAAC,GAAG,CACjC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC3D,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,2CAA0B,CAAC,GAAG,CAC5B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACtD,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;KACF,CAAC,CACH;IACD,cAAc,EAAE,iDAA6B,CAAC,QAAQ,EAAE;IACxD,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,6BAAmB,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC1D,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC;QAClB,yCAAyB;QACzB,iDAA6B;KAC9B,CAAC,CAAC,QAAQ,EAAE;IACb,eAAe,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACtC,gBAAgB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACvC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC1C,UAAU,EAAE,yCAAyB,CAAC,QAAQ,EAAE;IAChD,iBAAiB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;IACzC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,uDAAgC,CAAC,CAAC,QAAQ,EAAE;IACnE,UAAU,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;CACnC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,IAAI,EAAE,OAAO;QACb,SAAS,EAAE,YAAY;QACvB,UAAU,EAAE,aAAa;QACzB,cAAc,EAAE,iBAAiB;QACjC,UAAU,EAAE,aAAa;QACzB,eAAe,EAAE,kBAAkB;QACnC,gBAAgB,EAAE,mBAAmB;QACrC,iBAAiB,EAAE,qBAAqB;QACxC,UAAU,EAAE,aAAa;QACzB,UAAU,EAAE,aAAa;KAC1B,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,sBAAsB,CAOtC;AAPD,WAAiB,sBAAsB;IACrC,qEAAqE;IACxD,oCAAa,GAAG,2CAAmC,CAAC;IACjE,sEAAsE;IACzD,qCAAc,GAAG,4CAAoC,CAAC;AAGrE,CAAC,EAPgB,sBAAsB,sCAAtB,sBAAsB,QAOtC;AAED,SAAgB,2BAA2B,CACzC,qBAA4C;IAE5C,OAAO,IAAI,CAAC,SAAS,CACnB,4CAAoC,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAClE,CAAC;AACJ,CAAC;AAED,SAAgB,6BAA6B,CAC3C,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,2CAAmC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC/D,mDAAmD,CACpD,CAAC;AACJ,CAAC"}