"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WandbIntegration$ = exports.WandbIntegration$outboundSchema = exports.WandbIntegration$inboundSchema = exports.WandbIntegrationType$ = exports.WandbIntegrationType$outboundSchema = exports.WandbIntegrationType$inboundSchema = exports.WandbIntegrationType = void 0;
exports.wandbIntegrationToJSON = wandbIntegrationToJSON;
exports.wandbIntegrationFromJSON = wandbIntegrationFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
exports.WandbIntegrationType = {
    Wandb: "wandb",
};
/** @internal */
exports.WandbIntegrationType$inboundSchema = z.nativeEnum(exports.WandbIntegrationType);
/** @internal */
exports.WandbIntegrationType$outboundSchema = exports.WandbIntegrationType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WandbIntegrationType$;
(function (WandbIntegrationType$) {
    /** @deprecated use `WandbIntegrationType$inboundSchema` instead. */
    WandbIntegrationType$.inboundSchema = exports.WandbIntegrationType$inboundSchema;
    /** @deprecated use `WandbIntegrationType$outboundSchema` instead. */
    WandbIntegrationType$.outboundSchema = exports.WandbIntegrationType$outboundSchema;
})(WandbIntegrationType$ || (exports.WandbIntegrationType$ = WandbIntegrationType$ = {}));
/** @internal */
exports.WandbIntegration$inboundSchema = z.object({
    type: exports.WandbIntegrationType$inboundSchema.default("wandb"),
    project: z.string(),
    name: z.nullable(z.string()).optional(),
    api_key: z.string(),
    run_name: z.nullable(z.string()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "api_key": "apiKey",
        "run_name": "runName",
    });
});
/** @internal */
exports.WandbIntegration$outboundSchema = z.object({
    type: exports.WandbIntegrationType$outboundSchema.default("wandb"),
    project: z.string(),
    name: z.nullable(z.string()).optional(),
    apiKey: z.string(),
    runName: z.nullable(z.string()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        apiKey: "api_key",
        runName: "run_name",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WandbIntegration$;
(function (WandbIntegration$) {
    /** @deprecated use `WandbIntegration$inboundSchema` instead. */
    WandbIntegration$.inboundSchema = exports.WandbIntegration$inboundSchema;
    /** @deprecated use `WandbIntegration$outboundSchema` instead. */
    WandbIntegration$.outboundSchema = exports.WandbIntegration$outboundSchema;
})(WandbIntegration$ || (exports.WandbIntegration$ = WandbIntegration$ = {}));
function wandbIntegrationToJSON(wandbIntegration) {
    return JSON.stringify(exports.WandbIntegration$outboundSchema.parse(wandbIntegration));
}
function wandbIntegrationFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.WandbIntegration$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'WandbIntegration' from JSON`);
}
//# sourceMappingURL=wandbintegration.js.map