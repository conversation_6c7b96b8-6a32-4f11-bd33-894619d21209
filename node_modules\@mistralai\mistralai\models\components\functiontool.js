"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FunctionTool$ = exports.FunctionTool$outboundSchema = exports.FunctionTool$inboundSchema = exports.FunctionToolType$ = exports.FunctionToolType$outboundSchema = exports.FunctionToolType$inboundSchema = exports.FunctionToolType = void 0;
exports.functionToolToJSON = functionToolToJSON;
exports.functionToolFromJSON = functionToolFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const function_js_1 = require("./function.js");
exports.FunctionToolType = {
    Function: "function",
};
/** @internal */
exports.FunctionToolType$inboundSchema = z.nativeEnum(exports.FunctionToolType);
/** @internal */
exports.FunctionToolType$outboundSchema = exports.FunctionToolType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FunctionToolType$;
(function (FunctionToolType$) {
    /** @deprecated use `FunctionToolType$inboundSchema` instead. */
    FunctionToolType$.inboundSchema = exports.FunctionToolType$inboundSchema;
    /** @deprecated use `FunctionToolType$outboundSchema` instead. */
    FunctionToolType$.outboundSchema = exports.FunctionToolType$outboundSchema;
})(FunctionToolType$ || (exports.FunctionToolType$ = FunctionToolType$ = {}));
/** @internal */
exports.FunctionTool$inboundSchema = z.object({
    type: exports.FunctionToolType$inboundSchema.default("function"),
    function: function_js_1.FunctionT$inboundSchema,
});
/** @internal */
exports.FunctionTool$outboundSchema = z.object({
    type: exports.FunctionToolType$outboundSchema.default("function"),
    function: function_js_1.FunctionT$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FunctionTool$;
(function (FunctionTool$) {
    /** @deprecated use `FunctionTool$inboundSchema` instead. */
    FunctionTool$.inboundSchema = exports.FunctionTool$inboundSchema;
    /** @deprecated use `FunctionTool$outboundSchema` instead. */
    FunctionTool$.outboundSchema = exports.FunctionTool$outboundSchema;
})(FunctionTool$ || (exports.FunctionTool$ = FunctionTool$ = {}));
function functionToolToJSON(functionTool) {
    return JSON.stringify(exports.FunctionTool$outboundSchema.parse(functionTool));
}
function functionToolFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.FunctionTool$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'FunctionTool' from JSON`);
}
//# sourceMappingURL=functiontool.js.map