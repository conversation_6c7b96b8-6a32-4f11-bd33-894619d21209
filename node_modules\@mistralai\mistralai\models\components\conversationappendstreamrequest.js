"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationAppendStreamRequest$ = exports.ConversationAppendStreamRequest$outboundSchema = exports.ConversationAppendStreamRequest$inboundSchema = exports.ConversationAppendStreamRequestHandoffExecution$ = exports.ConversationAppendStreamRequestHandoffExecution$outboundSchema = exports.ConversationAppendStreamRequestHandoffExecution$inboundSchema = exports.ConversationAppendStreamRequestHandoffExecution = void 0;
exports.conversationAppendStreamRequestToJSON = conversationAppendStreamRequestToJSON;
exports.conversationAppendStreamRequestFromJSON = conversationAppendStreamRequestFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const completionargs_js_1 = require("./completionargs.js");
const conversationinputs_js_1 = require("./conversationinputs.js");
exports.ConversationAppendStreamRequestHandoffExecution = {
    Client: "client",
    Server: "server",
};
/** @internal */
exports.ConversationAppendStreamRequestHandoffExecution$inboundSchema = z
    .nativeEnum(exports.ConversationAppendStreamRequestHandoffExecution);
/** @internal */
exports.ConversationAppendStreamRequestHandoffExecution$outboundSchema = exports.ConversationAppendStreamRequestHandoffExecution$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationAppendStreamRequestHandoffExecution$;
(function (ConversationAppendStreamRequestHandoffExecution$) {
    /** @deprecated use `ConversationAppendStreamRequestHandoffExecution$inboundSchema` instead. */
    ConversationAppendStreamRequestHandoffExecution$.inboundSchema = exports.ConversationAppendStreamRequestHandoffExecution$inboundSchema;
    /** @deprecated use `ConversationAppendStreamRequestHandoffExecution$outboundSchema` instead. */
    ConversationAppendStreamRequestHandoffExecution$.outboundSchema = exports.ConversationAppendStreamRequestHandoffExecution$outboundSchema;
})(ConversationAppendStreamRequestHandoffExecution$ || (exports.ConversationAppendStreamRequestHandoffExecution$ = ConversationAppendStreamRequestHandoffExecution$ = {}));
/** @internal */
exports.ConversationAppendStreamRequest$inboundSchema = z.object({
    inputs: conversationinputs_js_1.ConversationInputs$inboundSchema,
    stream: z.boolean().default(true),
    store: z.boolean().default(true),
    handoff_execution: exports.ConversationAppendStreamRequestHandoffExecution$inboundSchema.default("server"),
    completion_args: completionargs_js_1.CompletionArgs$inboundSchema.optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "handoff_execution": "handoffExecution",
        "completion_args": "completionArgs",
    });
});
/** @internal */
exports.ConversationAppendStreamRequest$outboundSchema = z.object({
    inputs: conversationinputs_js_1.ConversationInputs$outboundSchema,
    stream: z.boolean().default(true),
    store: z.boolean().default(true),
    handoffExecution: exports.ConversationAppendStreamRequestHandoffExecution$outboundSchema.default("server"),
    completionArgs: completionargs_js_1.CompletionArgs$outboundSchema.optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        handoffExecution: "handoff_execution",
        completionArgs: "completion_args",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationAppendStreamRequest$;
(function (ConversationAppendStreamRequest$) {
    /** @deprecated use `ConversationAppendStreamRequest$inboundSchema` instead. */
    ConversationAppendStreamRequest$.inboundSchema = exports.ConversationAppendStreamRequest$inboundSchema;
    /** @deprecated use `ConversationAppendStreamRequest$outboundSchema` instead. */
    ConversationAppendStreamRequest$.outboundSchema = exports.ConversationAppendStreamRequest$outboundSchema;
})(ConversationAppendStreamRequest$ || (exports.ConversationAppendStreamRequest$ = ConversationAppendStreamRequest$ = {}));
function conversationAppendStreamRequestToJSON(conversationAppendStreamRequest) {
    return JSON.stringify(exports.ConversationAppendStreamRequest$outboundSchema.parse(conversationAppendStreamRequest));
}
function conversationAppendStreamRequestFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ConversationAppendStreamRequest$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ConversationAppendStreamRequest' from JSON`);
}
//# sourceMappingURL=conversationappendstreamrequest.js.map