"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SSETypes$ = exports.SSETypes$outboundSchema = exports.SSETypes$inboundSchema = exports.SSETypes = void 0;
const z = __importStar(require("zod"));
/**
 * Server side events sent when streaming a conversation response.
 */
exports.SSETypes = {
    ConversationResponseStarted: "conversation.response.started",
    ConversationResponseDone: "conversation.response.done",
    ConversationResponseError: "conversation.response.error",
    MessageOutputDelta: "message.output.delta",
    ToolExecutionStarted: "tool.execution.started",
    ToolExecutionDone: "tool.execution.done",
    AgentHandoffStarted: "agent.handoff.started",
    AgentHandoffDone: "agent.handoff.done",
    FunctionCallDelta: "function.call.delta",
};
/** @internal */
exports.SSETypes$inboundSchema = z
    .nativeEnum(exports.SSETypes);
/** @internal */
exports.SSETypes$outboundSchema = exports.SSETypes$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var SSETypes$;
(function (SSETypes$) {
    /** @deprecated use `SSETypes$inboundSchema` instead. */
    SSETypes$.inboundSchema = exports.SSETypes$inboundSchema;
    /** @deprecated use `SSETypes$outboundSchema` instead. */
    SSETypes$.outboundSchema = exports.SSETypes$outboundSchema;
})(SSETypes$ || (exports.SSETypes$ = SSETypes$ = {}));
//# sourceMappingURL=ssetypes.js.map