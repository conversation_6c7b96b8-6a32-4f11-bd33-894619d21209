"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserMessage$ = exports.UserMessage$outboundSchema = exports.UserMessage$inboundSchema = exports.UserMessageRole$ = exports.UserMessageRole$outboundSchema = exports.UserMessageRole$inboundSchema = exports.UserMessageContent$ = exports.UserMessageContent$outboundSchema = exports.UserMessageContent$inboundSchema = exports.UserMessageRole = void 0;
exports.userMessageContentToJSON = userMessageContentToJSON;
exports.userMessageContentFromJSON = userMessageContentFromJSON;
exports.userMessageToJSON = userMessageToJSON;
exports.userMessageFromJSON = userMessageFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const contentchunk_js_1 = require("./contentchunk.js");
exports.UserMessageRole = {
    User: "user",
};
/** @internal */
exports.UserMessageContent$inboundSchema = z.union([z.string(), z.array(contentchunk_js_1.ContentChunk$inboundSchema)]);
/** @internal */
exports.UserMessageContent$outboundSchema = z.union([z.string(), z.array(contentchunk_js_1.ContentChunk$outboundSchema)]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var UserMessageContent$;
(function (UserMessageContent$) {
    /** @deprecated use `UserMessageContent$inboundSchema` instead. */
    UserMessageContent$.inboundSchema = exports.UserMessageContent$inboundSchema;
    /** @deprecated use `UserMessageContent$outboundSchema` instead. */
    UserMessageContent$.outboundSchema = exports.UserMessageContent$outboundSchema;
})(UserMessageContent$ || (exports.UserMessageContent$ = UserMessageContent$ = {}));
function userMessageContentToJSON(userMessageContent) {
    return JSON.stringify(exports.UserMessageContent$outboundSchema.parse(userMessageContent));
}
function userMessageContentFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.UserMessageContent$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'UserMessageContent' from JSON`);
}
/** @internal */
exports.UserMessageRole$inboundSchema = z.nativeEnum(exports.UserMessageRole);
/** @internal */
exports.UserMessageRole$outboundSchema = exports.UserMessageRole$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var UserMessageRole$;
(function (UserMessageRole$) {
    /** @deprecated use `UserMessageRole$inboundSchema` instead. */
    UserMessageRole$.inboundSchema = exports.UserMessageRole$inboundSchema;
    /** @deprecated use `UserMessageRole$outboundSchema` instead. */
    UserMessageRole$.outboundSchema = exports.UserMessageRole$outboundSchema;
})(UserMessageRole$ || (exports.UserMessageRole$ = UserMessageRole$ = {}));
/** @internal */
exports.UserMessage$inboundSchema = z.object({
    content: z.nullable(z.union([z.string(), z.array(contentchunk_js_1.ContentChunk$inboundSchema)])),
    role: exports.UserMessageRole$inboundSchema.default("user"),
});
/** @internal */
exports.UserMessage$outboundSchema = z.object({
    content: z.nullable(z.union([z.string(), z.array(contentchunk_js_1.ContentChunk$outboundSchema)])),
    role: exports.UserMessageRole$outboundSchema.default("user"),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var UserMessage$;
(function (UserMessage$) {
    /** @deprecated use `UserMessage$inboundSchema` instead. */
    UserMessage$.inboundSchema = exports.UserMessage$inboundSchema;
    /** @deprecated use `UserMessage$outboundSchema` instead. */
    UserMessage$.outboundSchema = exports.UserMessage$outboundSchema;
})(UserMessage$ || (exports.UserMessage$ = UserMessage$ = {}));
function userMessageToJSON(userMessage) {
    return JSON.stringify(exports.UserMessage$outboundSchema.parse(userMessage));
}
function userMessageFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.UserMessage$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'UserMessage' from JSON`);
}
//# sourceMappingURL=usermessage.js.map