"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ListFilesOut$ = exports.ListFilesOut$outboundSchema = exports.ListFilesOut$inboundSchema = void 0;
exports.listFilesOutToJSON = listFilesOutToJSON;
exports.listFilesOutFromJSON = listFilesOutFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const fileschema_js_1 = require("./fileschema.js");
/** @internal */
exports.ListFilesOut$inboundSchema = z.object({
    data: z.array(fileschema_js_1.FileSchema$inboundSchema),
    object: z.string(),
    total: z.number().int(),
});
/** @internal */
exports.ListFilesOut$outboundSchema = z.object({
    data: z.array(fileschema_js_1.FileSchema$outboundSchema),
    object: z.string(),
    total: z.number().int(),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ListFilesOut$;
(function (ListFilesOut$) {
    /** @deprecated use `ListFilesOut$inboundSchema` instead. */
    ListFilesOut$.inboundSchema = exports.ListFilesOut$inboundSchema;
    /** @deprecated use `ListFilesOut$outboundSchema` instead. */
    ListFilesOut$.outboundSchema = exports.ListFilesOut$outboundSchema;
})(ListFilesOut$ || (exports.ListFilesOut$ = ListFilesOut$ = {}));
function listFilesOutToJSON(listFilesOut) {
    return JSON.stringify(exports.ListFilesOut$outboundSchema.parse(listFilesOut));
}
function listFilesOutFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ListFilesOut$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ListFilesOut' from JSON`);
}
//# sourceMappingURL=listfilesout.js.map