import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";
export declare const FTClassifierLossFunction: {
    readonly SingleClass: "single_class";
    readonly MultiClass: "multi_class";
};
export type FTClassifierLossFunction = ClosedEnum<typeof FTClassifierLossFunction>;
/** @internal */
export declare const FTClassifierLossFunction$inboundSchema: z.ZodNativeEnum<typeof FTClassifierLossFunction>;
/** @internal */
export declare const FTClassifierLossFunction$outboundSchema: z.ZodNativeEnum<typeof FTClassifierLossFunction>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace FTClassifierLossFunction$ {
    /** @deprecated use `FTClassifierLossFunction$inboundSchema` instead. */
    const inboundSchema: z.ZodNativeEnum<{
        readonly SingleClass: "single_class";
        readonly MultiClass: "multi_class";
    }>;
    /** @deprecated use `FTClassifierLossFunction$outboundSchema` instead. */
    const outboundSchema: z.ZodNativeEnum<{
        readonly SingleClass: "single_class";
        readonly MultiClass: "multi_class";
    }>;
}
//# sourceMappingURL=ftclassifierlossfunction.d.ts.map