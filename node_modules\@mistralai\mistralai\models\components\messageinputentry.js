"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageInputEntry$ = exports.MessageInputEntry$outboundSchema = exports.MessageInputEntry$inboundSchema = exports.MessageInputEntryContent$ = exports.MessageInputEntryContent$outboundSchema = exports.MessageInputEntryContent$inboundSchema = exports.MessageInputEntryRole$ = exports.MessageInputEntryRole$outboundSchema = exports.MessageInputEntryRole$inboundSchema = exports.MessageInputEntryType$ = exports.MessageInputEntryType$outboundSchema = exports.MessageInputEntryType$inboundSchema = exports.ObjectT$ = exports.ObjectT$outboundSchema = exports.ObjectT$inboundSchema = exports.MessageInputEntryRole = exports.MessageInputEntryType = exports.ObjectT = void 0;
exports.messageInputEntryContentToJSON = messageInputEntryContentToJSON;
exports.messageInputEntryContentFromJSON = messageInputEntryContentFromJSON;
exports.messageInputEntryToJSON = messageInputEntryToJSON;
exports.messageInputEntryFromJSON = messageInputEntryFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const messageinputcontentchunks_js_1 = require("./messageinputcontentchunks.js");
exports.ObjectT = {
    Entry: "entry",
};
exports.MessageInputEntryType = {
    MessageInput: "message.input",
};
exports.MessageInputEntryRole = {
    Assistant: "assistant",
    User: "user",
};
/** @internal */
exports.ObjectT$inboundSchema = z
    .nativeEnum(exports.ObjectT);
/** @internal */
exports.ObjectT$outboundSchema = exports.ObjectT$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ObjectT$;
(function (ObjectT$) {
    /** @deprecated use `ObjectT$inboundSchema` instead. */
    ObjectT$.inboundSchema = exports.ObjectT$inboundSchema;
    /** @deprecated use `ObjectT$outboundSchema` instead. */
    ObjectT$.outboundSchema = exports.ObjectT$outboundSchema;
})(ObjectT$ || (exports.ObjectT$ = ObjectT$ = {}));
/** @internal */
exports.MessageInputEntryType$inboundSchema = z.nativeEnum(exports.MessageInputEntryType);
/** @internal */
exports.MessageInputEntryType$outboundSchema = exports.MessageInputEntryType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MessageInputEntryType$;
(function (MessageInputEntryType$) {
    /** @deprecated use `MessageInputEntryType$inboundSchema` instead. */
    MessageInputEntryType$.inboundSchema = exports.MessageInputEntryType$inboundSchema;
    /** @deprecated use `MessageInputEntryType$outboundSchema` instead. */
    MessageInputEntryType$.outboundSchema = exports.MessageInputEntryType$outboundSchema;
})(MessageInputEntryType$ || (exports.MessageInputEntryType$ = MessageInputEntryType$ = {}));
/** @internal */
exports.MessageInputEntryRole$inboundSchema = z.nativeEnum(exports.MessageInputEntryRole);
/** @internal */
exports.MessageInputEntryRole$outboundSchema = exports.MessageInputEntryRole$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MessageInputEntryRole$;
(function (MessageInputEntryRole$) {
    /** @deprecated use `MessageInputEntryRole$inboundSchema` instead. */
    MessageInputEntryRole$.inboundSchema = exports.MessageInputEntryRole$inboundSchema;
    /** @deprecated use `MessageInputEntryRole$outboundSchema` instead. */
    MessageInputEntryRole$.outboundSchema = exports.MessageInputEntryRole$outboundSchema;
})(MessageInputEntryRole$ || (exports.MessageInputEntryRole$ = MessageInputEntryRole$ = {}));
/** @internal */
exports.MessageInputEntryContent$inboundSchema = z.union([z.string(), z.array(messageinputcontentchunks_js_1.MessageInputContentChunks$inboundSchema)]);
/** @internal */
exports.MessageInputEntryContent$outboundSchema = z.union([z.string(), z.array(messageinputcontentchunks_js_1.MessageInputContentChunks$outboundSchema)]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MessageInputEntryContent$;
(function (MessageInputEntryContent$) {
    /** @deprecated use `MessageInputEntryContent$inboundSchema` instead. */
    MessageInputEntryContent$.inboundSchema = exports.MessageInputEntryContent$inboundSchema;
    /** @deprecated use `MessageInputEntryContent$outboundSchema` instead. */
    MessageInputEntryContent$.outboundSchema = exports.MessageInputEntryContent$outboundSchema;
})(MessageInputEntryContent$ || (exports.MessageInputEntryContent$ = MessageInputEntryContent$ = {}));
function messageInputEntryContentToJSON(messageInputEntryContent) {
    return JSON.stringify(exports.MessageInputEntryContent$outboundSchema.parse(messageInputEntryContent));
}
function messageInputEntryContentFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.MessageInputEntryContent$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'MessageInputEntryContent' from JSON`);
}
/** @internal */
exports.MessageInputEntry$inboundSchema = z.object({
    object: exports.ObjectT$inboundSchema.default("entry"),
    type: exports.MessageInputEntryType$inboundSchema.default("message.input"),
    created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
        .optional(),
    completed_at: z.nullable(z.string().datetime({ offset: true }).transform(v => new Date(v))).optional(),
    id: z.string().optional(),
    role: exports.MessageInputEntryRole$inboundSchema,
    content: z.union([
        z.string(),
        z.array(messageinputcontentchunks_js_1.MessageInputContentChunks$inboundSchema),
    ]),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "created_at": "createdAt",
        "completed_at": "completedAt",
    });
});
/** @internal */
exports.MessageInputEntry$outboundSchema = z.object({
    object: exports.ObjectT$outboundSchema.default("entry"),
    type: exports.MessageInputEntryType$outboundSchema.default("message.input"),
    createdAt: z.date().transform(v => v.toISOString()).optional(),
    completedAt: z.nullable(z.date().transform(v => v.toISOString())).optional(),
    id: z.string().optional(),
    role: exports.MessageInputEntryRole$outboundSchema,
    content: z.union([
        z.string(),
        z.array(messageinputcontentchunks_js_1.MessageInputContentChunks$outboundSchema),
    ]),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        createdAt: "created_at",
        completedAt: "completed_at",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MessageInputEntry$;
(function (MessageInputEntry$) {
    /** @deprecated use `MessageInputEntry$inboundSchema` instead. */
    MessageInputEntry$.inboundSchema = exports.MessageInputEntry$inboundSchema;
    /** @deprecated use `MessageInputEntry$outboundSchema` instead. */
    MessageInputEntry$.outboundSchema = exports.MessageInputEntry$outboundSchema;
})(MessageInputEntry$ || (exports.MessageInputEntry$ = MessageInputEntry$ = {}));
function messageInputEntryToJSON(messageInputEntry) {
    return JSON.stringify(exports.MessageInputEntry$outboundSchema.parse(messageInputEntry));
}
function messageInputEntryFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.MessageInputEntry$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'MessageInputEntry' from JSON`);
}
//# sourceMappingURL=messageinputentry.js.map