"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompletionArgs$ = exports.CompletionArgs$outboundSchema = exports.CompletionArgs$inboundSchema = void 0;
exports.completionArgsToJSON = completionArgsToJSON;
exports.completionArgsFromJSON = completionArgsFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const completionargsstop_js_1 = require("./completionargsstop.js");
const prediction_js_1 = require("./prediction.js");
const responseformat_js_1 = require("./responseformat.js");
const toolchoiceenum_js_1 = require("./toolchoiceenum.js");
/** @internal */
exports.CompletionArgs$inboundSchema = z.object({
    stop: z.nullable(completionargsstop_js_1.CompletionArgsStop$inboundSchema).optional(),
    presence_penalty: z.nullable(z.number()).optional(),
    frequency_penalty: z.nullable(z.number()).optional(),
    temperature: z.number().default(0.3),
    top_p: z.nullable(z.number()).optional(),
    max_tokens: z.nullable(z.number().int()).optional(),
    random_seed: z.nullable(z.number().int()).optional(),
    prediction: z.nullable(prediction_js_1.Prediction$inboundSchema).optional(),
    response_format: z.nullable(responseformat_js_1.ResponseFormat$inboundSchema).optional(),
    tool_choice: toolchoiceenum_js_1.ToolChoiceEnum$inboundSchema.optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "presence_penalty": "presencePenalty",
        "frequency_penalty": "frequencyPenalty",
        "top_p": "topP",
        "max_tokens": "maxTokens",
        "random_seed": "randomSeed",
        "response_format": "responseFormat",
        "tool_choice": "toolChoice",
    });
});
/** @internal */
exports.CompletionArgs$outboundSchema = z.object({
    stop: z.nullable(completionargsstop_js_1.CompletionArgsStop$outboundSchema).optional(),
    presencePenalty: z.nullable(z.number()).optional(),
    frequencyPenalty: z.nullable(z.number()).optional(),
    temperature: z.number().default(0.3),
    topP: z.nullable(z.number()).optional(),
    maxTokens: z.nullable(z.number().int()).optional(),
    randomSeed: z.nullable(z.number().int()).optional(),
    prediction: z.nullable(prediction_js_1.Prediction$outboundSchema).optional(),
    responseFormat: z.nullable(responseformat_js_1.ResponseFormat$outboundSchema).optional(),
    toolChoice: toolchoiceenum_js_1.ToolChoiceEnum$outboundSchema.optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        presencePenalty: "presence_penalty",
        frequencyPenalty: "frequency_penalty",
        topP: "top_p",
        maxTokens: "max_tokens",
        randomSeed: "random_seed",
        responseFormat: "response_format",
        toolChoice: "tool_choice",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var CompletionArgs$;
(function (CompletionArgs$) {
    /** @deprecated use `CompletionArgs$inboundSchema` instead. */
    CompletionArgs$.inboundSchema = exports.CompletionArgs$inboundSchema;
    /** @deprecated use `CompletionArgs$outboundSchema` instead. */
    CompletionArgs$.outboundSchema = exports.CompletionArgs$outboundSchema;
})(CompletionArgs$ || (exports.CompletionArgs$ = CompletionArgs$ = {}));
function completionArgsToJSON(completionArgs) {
    return JSON.stringify(exports.CompletionArgs$outboundSchema.parse(completionArgs));
}
function completionArgsFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.CompletionArgs$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'CompletionArgs' from JSON`);
}
//# sourceMappingURL=completionargs.js.map