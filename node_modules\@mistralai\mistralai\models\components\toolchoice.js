"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolChoice$ = exports.ToolChoice$outboundSchema = exports.ToolChoice$inboundSchema = void 0;
exports.toolChoiceToJSON = toolChoiceToJSON;
exports.toolChoiceFromJSON = toolChoiceFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const functionname_js_1 = require("./functionname.js");
const tooltypes_js_1 = require("./tooltypes.js");
/** @internal */
exports.ToolChoice$inboundSchema = z.object({
    type: tooltypes_js_1.ToolTypes$inboundSchema.optional(),
    function: functionname_js_1.FunctionName$inboundSchema,
});
/** @internal */
exports.ToolChoice$outboundSchema = z.object({
    type: tooltypes_js_1.ToolTypes$outboundSchema.optional(),
    function: functionname_js_1.FunctionName$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ToolChoice$;
(function (ToolChoice$) {
    /** @deprecated use `ToolChoice$inboundSchema` instead. */
    ToolChoice$.inboundSchema = exports.ToolChoice$inboundSchema;
    /** @deprecated use `ToolChoice$outboundSchema` instead. */
    ToolChoice$.outboundSchema = exports.ToolChoice$outboundSchema;
})(ToolChoice$ || (exports.ToolChoice$ = ToolChoice$ = {}));
function toolChoiceToJSON(toolChoice) {
    return JSON.stringify(exports.ToolChoice$outboundSchema.parse(toolChoice));
}
function toolChoiceFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ToolChoice$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ToolChoice' from JSON`);
}
//# sourceMappingURL=toolchoice.js.map