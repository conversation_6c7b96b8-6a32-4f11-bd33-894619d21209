"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentChunk$ = exports.ContentChunk$outboundSchema = exports.ContentChunk$inboundSchema = void 0;
exports.contentChunkToJSON = contentChunkToJSON;
exports.contentChunkFromJSON = contentChunkFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const documenturlchunk_js_1 = require("./documenturlchunk.js");
const imageurlchunk_js_1 = require("./imageurlchunk.js");
const referencechunk_js_1 = require("./referencechunk.js");
const textchunk_js_1 = require("./textchunk.js");
/** @internal */
exports.ContentChunk$inboundSchema = z.union([
    imageurlchunk_js_1.ImageURLChunk$inboundSchema.and(z.object({ type: z.literal("image_url") }).transform((v) => ({
        type: v.type,
    }))),
    textchunk_js_1.TextChunk$inboundSchema.and(z.object({ type: z.literal("text") }).transform((v) => ({ type: v.type }))),
    referencechunk_js_1.ReferenceChunk$inboundSchema.and(z.object({ type: z.literal("reference") }).transform((v) => ({
        type: v.type,
    }))),
    documenturlchunk_js_1.DocumentURLChunk$inboundSchema.and(z.object({ type: z.literal("document_url") }).transform((v) => ({
        type: v.type,
    }))),
]);
/** @internal */
exports.ContentChunk$outboundSchema = z.union([
    imageurlchunk_js_1.ImageURLChunk$outboundSchema.and(z.object({ type: z.literal("image_url") }).transform((v) => ({
        type: v.type,
    }))),
    textchunk_js_1.TextChunk$outboundSchema.and(z.object({ type: z.literal("text") }).transform((v) => ({ type: v.type }))),
    referencechunk_js_1.ReferenceChunk$outboundSchema.and(z.object({ type: z.literal("reference") }).transform((v) => ({
        type: v.type,
    }))),
    documenturlchunk_js_1.DocumentURLChunk$outboundSchema.and(z.object({ type: z.literal("document_url") }).transform((v) => ({
        type: v.type,
    }))),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ContentChunk$;
(function (ContentChunk$) {
    /** @deprecated use `ContentChunk$inboundSchema` instead. */
    ContentChunk$.inboundSchema = exports.ContentChunk$inboundSchema;
    /** @deprecated use `ContentChunk$outboundSchema` instead. */
    ContentChunk$.outboundSchema = exports.ContentChunk$outboundSchema;
})(ContentChunk$ || (exports.ContentChunk$ = ContentChunk$ = {}));
function contentChunkToJSON(contentChunk) {
    return JSON.stringify(exports.ContentChunk$outboundSchema.parse(contentChunk));
}
function contentChunkFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ContentChunk$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ContentChunk' from JSON`);
}
//# sourceMappingURL=contentchunk.js.map