"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InstructRequest$ = exports.InstructRequest$outboundSchema = exports.InstructRequest$inboundSchema = exports.InstructRequestMessages$ = exports.InstructRequestMessages$outboundSchema = exports.InstructRequestMessages$inboundSchema = void 0;
exports.instructRequestMessagesToJSON = instructRequestMessagesToJSON;
exports.instructRequestMessagesFromJSON = instructRequestMessagesFromJSON;
exports.instructRequestToJSON = instructRequestToJSON;
exports.instructRequestFromJSON = instructRequestFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const assistantmessage_js_1 = require("./assistantmessage.js");
const systemmessage_js_1 = require("./systemmessage.js");
const toolmessage_js_1 = require("./toolmessage.js");
const usermessage_js_1 = require("./usermessage.js");
/** @internal */
exports.InstructRequestMessages$inboundSchema = z.union([
    systemmessage_js_1.SystemMessage$inboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
        role: v.role,
    }))),
    usermessage_js_1.UserMessage$inboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({ role: v.role }))),
    assistantmessage_js_1.AssistantMessage$inboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
        role: v.role,
    }))),
    toolmessage_js_1.ToolMessage$inboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({ role: v.role }))),
]);
/** @internal */
exports.InstructRequestMessages$outboundSchema = z.union([
    systemmessage_js_1.SystemMessage$outboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
        role: v.role,
    }))),
    usermessage_js_1.UserMessage$outboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({ role: v.role }))),
    assistantmessage_js_1.AssistantMessage$outboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
        role: v.role,
    }))),
    toolmessage_js_1.ToolMessage$outboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({ role: v.role }))),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var InstructRequestMessages$;
(function (InstructRequestMessages$) {
    /** @deprecated use `InstructRequestMessages$inboundSchema` instead. */
    InstructRequestMessages$.inboundSchema = exports.InstructRequestMessages$inboundSchema;
    /** @deprecated use `InstructRequestMessages$outboundSchema` instead. */
    InstructRequestMessages$.outboundSchema = exports.InstructRequestMessages$outboundSchema;
})(InstructRequestMessages$ || (exports.InstructRequestMessages$ = InstructRequestMessages$ = {}));
function instructRequestMessagesToJSON(instructRequestMessages) {
    return JSON.stringify(exports.InstructRequestMessages$outboundSchema.parse(instructRequestMessages));
}
function instructRequestMessagesFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.InstructRequestMessages$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'InstructRequestMessages' from JSON`);
}
/** @internal */
exports.InstructRequest$inboundSchema = z.object({
    messages: z.array(z.union([
        systemmessage_js_1.SystemMessage$inboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
            role: v.role,
        }))),
        usermessage_js_1.UserMessage$inboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({
            role: v.role,
        }))),
        assistantmessage_js_1.AssistantMessage$inboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
            role: v.role,
        }))),
        toolmessage_js_1.ToolMessage$inboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({
            role: v.role,
        }))),
    ])),
});
/** @internal */
exports.InstructRequest$outboundSchema = z.object({
    messages: z.array(z.union([
        systemmessage_js_1.SystemMessage$outboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
            role: v.role,
        }))),
        usermessage_js_1.UserMessage$outboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({
            role: v.role,
        }))),
        assistantmessage_js_1.AssistantMessage$outboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
            role: v.role,
        }))),
        toolmessage_js_1.ToolMessage$outboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({
            role: v.role,
        }))),
    ])),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var InstructRequest$;
(function (InstructRequest$) {
    /** @deprecated use `InstructRequest$inboundSchema` instead. */
    InstructRequest$.inboundSchema = exports.InstructRequest$inboundSchema;
    /** @deprecated use `InstructRequest$outboundSchema` instead. */
    InstructRequest$.outboundSchema = exports.InstructRequest$outboundSchema;
})(InstructRequest$ || (exports.InstructRequest$ = InstructRequest$ = {}));
function instructRequestToJSON(instructRequest) {
    return JSON.stringify(exports.InstructRequest$outboundSchema.parse(instructRequest));
}
function instructRequestFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.InstructRequest$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'InstructRequest' from JSON`);
}
//# sourceMappingURL=instructrequest.js.map