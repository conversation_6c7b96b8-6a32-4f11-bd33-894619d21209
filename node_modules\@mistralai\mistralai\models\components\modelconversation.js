"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelConversation$ = exports.ModelConversation$outboundSchema = exports.ModelConversation$inboundSchema = exports.ModelConversationObject$ = exports.ModelConversationObject$outboundSchema = exports.ModelConversationObject$inboundSchema = exports.ModelConversationTools$ = exports.ModelConversationTools$outboundSchema = exports.ModelConversationTools$inboundSchema = exports.ModelConversationObject = void 0;
exports.modelConversationToolsToJSON = modelConversationToolsToJSON;
exports.modelConversationToolsFromJSON = modelConversationToolsFromJSON;
exports.modelConversationToJSON = modelConversationToJSON;
exports.modelConversationFromJSON = modelConversationFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const codeinterpretertool_js_1 = require("./codeinterpretertool.js");
const completionargs_js_1 = require("./completionargs.js");
const documentlibrarytool_js_1 = require("./documentlibrarytool.js");
const functiontool_js_1 = require("./functiontool.js");
const imagegenerationtool_js_1 = require("./imagegenerationtool.js");
const websearchpremiumtool_js_1 = require("./websearchpremiumtool.js");
const websearchtool_js_1 = require("./websearchtool.js");
exports.ModelConversationObject = {
    Conversation: "conversation",
};
/** @internal */
exports.ModelConversationTools$inboundSchema = z.union([
    codeinterpretertool_js_1.CodeInterpreterTool$inboundSchema.and(z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
        type: v.type,
    }))),
    imagegenerationtool_js_1.ImageGenerationTool$inboundSchema.and(z.object({ type: z.literal("image_generation") }).transform((v) => ({
        type: v.type,
    }))),
    websearchtool_js_1.WebSearchTool$inboundSchema.and(z.object({ type: z.literal("web_search") }).transform((v) => ({
        type: v.type,
    }))),
    websearchpremiumtool_js_1.WebSearchPremiumTool$inboundSchema.and(z.object({ type: z.literal("web_search_premium") }).transform((v) => ({
        type: v.type,
    }))),
    documentlibrarytool_js_1.DocumentLibraryTool$inboundSchema.and(z.object({ type: z.literal("document_library") }).transform((v) => ({
        type: v.type,
    }))),
    functiontool_js_1.FunctionTool$inboundSchema.and(z.object({ type: z.literal("function") }).transform((v) => ({
        type: v.type,
    }))),
]);
/** @internal */
exports.ModelConversationTools$outboundSchema = z.union([
    codeinterpretertool_js_1.CodeInterpreterTool$outboundSchema.and(z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
        type: v.type,
    }))),
    imagegenerationtool_js_1.ImageGenerationTool$outboundSchema.and(z.object({ type: z.literal("image_generation") }).transform((v) => ({
        type: v.type,
    }))),
    websearchtool_js_1.WebSearchTool$outboundSchema.and(z.object({ type: z.literal("web_search") }).transform((v) => ({
        type: v.type,
    }))),
    websearchpremiumtool_js_1.WebSearchPremiumTool$outboundSchema.and(z.object({ type: z.literal("web_search_premium") }).transform((v) => ({
        type: v.type,
    }))),
    documentlibrarytool_js_1.DocumentLibraryTool$outboundSchema.and(z.object({ type: z.literal("document_library") }).transform((v) => ({
        type: v.type,
    }))),
    functiontool_js_1.FunctionTool$outboundSchema.and(z.object({ type: z.literal("function") }).transform((v) => ({
        type: v.type,
    }))),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ModelConversationTools$;
(function (ModelConversationTools$) {
    /** @deprecated use `ModelConversationTools$inboundSchema` instead. */
    ModelConversationTools$.inboundSchema = exports.ModelConversationTools$inboundSchema;
    /** @deprecated use `ModelConversationTools$outboundSchema` instead. */
    ModelConversationTools$.outboundSchema = exports.ModelConversationTools$outboundSchema;
})(ModelConversationTools$ || (exports.ModelConversationTools$ = ModelConversationTools$ = {}));
function modelConversationToolsToJSON(modelConversationTools) {
    return JSON.stringify(exports.ModelConversationTools$outboundSchema.parse(modelConversationTools));
}
function modelConversationToolsFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ModelConversationTools$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ModelConversationTools' from JSON`);
}
/** @internal */
exports.ModelConversationObject$inboundSchema = z.nativeEnum(exports.ModelConversationObject);
/** @internal */
exports.ModelConversationObject$outboundSchema = exports.ModelConversationObject$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ModelConversationObject$;
(function (ModelConversationObject$) {
    /** @deprecated use `ModelConversationObject$inboundSchema` instead. */
    ModelConversationObject$.inboundSchema = exports.ModelConversationObject$inboundSchema;
    /** @deprecated use `ModelConversationObject$outboundSchema` instead. */
    ModelConversationObject$.outboundSchema = exports.ModelConversationObject$outboundSchema;
})(ModelConversationObject$ || (exports.ModelConversationObject$ = ModelConversationObject$ = {}));
/** @internal */
exports.ModelConversation$inboundSchema = z.object({
    instructions: z.nullable(z.string()).optional(),
    tools: z.array(z.union([
        codeinterpretertool_js_1.CodeInterpreterTool$inboundSchema.and(z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
            type: v.type,
        }))),
        imagegenerationtool_js_1.ImageGenerationTool$inboundSchema.and(z.object({ type: z.literal("image_generation") }).transform((v) => ({
            type: v.type,
        }))),
        websearchtool_js_1.WebSearchTool$inboundSchema.and(z.object({ type: z.literal("web_search") }).transform((v) => ({
            type: v.type,
        }))),
        websearchpremiumtool_js_1.WebSearchPremiumTool$inboundSchema.and(z.object({ type: z.literal("web_search_premium") }).transform((v) => ({
            type: v.type,
        }))),
        documentlibrarytool_js_1.DocumentLibraryTool$inboundSchema.and(z.object({ type: z.literal("document_library") }).transform((v) => ({
            type: v.type,
        }))),
        functiontool_js_1.FunctionTool$inboundSchema.and(z.object({ type: z.literal("function") }).transform((v) => ({
            type: v.type,
        }))),
    ])).optional(),
    completion_args: completionargs_js_1.CompletionArgs$inboundSchema.optional(),
    name: z.nullable(z.string()).optional(),
    description: z.nullable(z.string()).optional(),
    object: exports.ModelConversationObject$inboundSchema.default("conversation"),
    id: z.string(),
    created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
    updated_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
    model: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "completion_args": "completionArgs",
        "created_at": "createdAt",
        "updated_at": "updatedAt",
    });
});
/** @internal */
exports.ModelConversation$outboundSchema = z.object({
    instructions: z.nullable(z.string()).optional(),
    tools: z.array(z.union([
        codeinterpretertool_js_1.CodeInterpreterTool$outboundSchema.and(z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
            type: v.type,
        }))),
        imagegenerationtool_js_1.ImageGenerationTool$outboundSchema.and(z.object({ type: z.literal("image_generation") }).transform((v) => ({
            type: v.type,
        }))),
        websearchtool_js_1.WebSearchTool$outboundSchema.and(z.object({ type: z.literal("web_search") }).transform((v) => ({
            type: v.type,
        }))),
        websearchpremiumtool_js_1.WebSearchPremiumTool$outboundSchema.and(z.object({ type: z.literal("web_search_premium") }).transform((v) => ({
            type: v.type,
        }))),
        documentlibrarytool_js_1.DocumentLibraryTool$outboundSchema.and(z.object({ type: z.literal("document_library") }).transform((v) => ({
            type: v.type,
        }))),
        functiontool_js_1.FunctionTool$outboundSchema.and(z.object({ type: z.literal("function") }).transform((v) => ({
            type: v.type,
        }))),
    ])).optional(),
    completionArgs: completionargs_js_1.CompletionArgs$outboundSchema.optional(),
    name: z.nullable(z.string()).optional(),
    description: z.nullable(z.string()).optional(),
    object: exports.ModelConversationObject$outboundSchema.default("conversation"),
    id: z.string(),
    createdAt: z.date().transform(v => v.toISOString()),
    updatedAt: z.date().transform(v => v.toISOString()),
    model: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        completionArgs: "completion_args",
        createdAt: "created_at",
        updatedAt: "updated_at",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ModelConversation$;
(function (ModelConversation$) {
    /** @deprecated use `ModelConversation$inboundSchema` instead. */
    ModelConversation$.inboundSchema = exports.ModelConversation$inboundSchema;
    /** @deprecated use `ModelConversation$outboundSchema` instead. */
    ModelConversation$.outboundSchema = exports.ModelConversation$outboundSchema;
})(ModelConversation$ || (exports.ModelConversation$ = ModelConversation$ = {}));
function modelConversationToJSON(modelConversation) {
    return JSON.stringify(exports.ModelConversation$outboundSchema.parse(modelConversation));
}
function modelConversationFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ModelConversation$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ModelConversation' from JSON`);
}
//# sourceMappingURL=modelconversation.js.map