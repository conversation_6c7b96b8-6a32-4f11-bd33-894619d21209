"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationAppendRequest$ = exports.ConversationAppendRequest$outboundSchema = exports.ConversationAppendRequest$inboundSchema = exports.ConversationAppendRequestHandoffExecution$ = exports.ConversationAppendRequestHandoffExecution$outboundSchema = exports.ConversationAppendRequestHandoffExecution$inboundSchema = exports.ConversationAppendRequestHandoffExecution = void 0;
exports.conversationAppendRequestToJSON = conversationAppendRequestToJSON;
exports.conversationAppendRequestFromJSON = conversationAppendRequestFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const completionargs_js_1 = require("./completionargs.js");
const conversationinputs_js_1 = require("./conversationinputs.js");
exports.ConversationAppendRequestHandoffExecution = {
    Client: "client",
    Server: "server",
};
/** @internal */
exports.ConversationAppendRequestHandoffExecution$inboundSchema = z
    .nativeEnum(exports.ConversationAppendRequestHandoffExecution);
/** @internal */
exports.ConversationAppendRequestHandoffExecution$outboundSchema = exports.ConversationAppendRequestHandoffExecution$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationAppendRequestHandoffExecution$;
(function (ConversationAppendRequestHandoffExecution$) {
    /** @deprecated use `ConversationAppendRequestHandoffExecution$inboundSchema` instead. */
    ConversationAppendRequestHandoffExecution$.inboundSchema = exports.ConversationAppendRequestHandoffExecution$inboundSchema;
    /** @deprecated use `ConversationAppendRequestHandoffExecution$outboundSchema` instead. */
    ConversationAppendRequestHandoffExecution$.outboundSchema = exports.ConversationAppendRequestHandoffExecution$outboundSchema;
})(ConversationAppendRequestHandoffExecution$ || (exports.ConversationAppendRequestHandoffExecution$ = ConversationAppendRequestHandoffExecution$ = {}));
/** @internal */
exports.ConversationAppendRequest$inboundSchema = z.object({
    inputs: conversationinputs_js_1.ConversationInputs$inboundSchema,
    stream: z.boolean().default(false),
    store: z.boolean().default(true),
    handoff_execution: exports.ConversationAppendRequestHandoffExecution$inboundSchema
        .default("server"),
    completion_args: completionargs_js_1.CompletionArgs$inboundSchema.optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "handoff_execution": "handoffExecution",
        "completion_args": "completionArgs",
    });
});
/** @internal */
exports.ConversationAppendRequest$outboundSchema = z.object({
    inputs: conversationinputs_js_1.ConversationInputs$outboundSchema,
    stream: z.boolean().default(false),
    store: z.boolean().default(true),
    handoffExecution: exports.ConversationAppendRequestHandoffExecution$outboundSchema
        .default("server"),
    completionArgs: completionargs_js_1.CompletionArgs$outboundSchema.optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        handoffExecution: "handoff_execution",
        completionArgs: "completion_args",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationAppendRequest$;
(function (ConversationAppendRequest$) {
    /** @deprecated use `ConversationAppendRequest$inboundSchema` instead. */
    ConversationAppendRequest$.inboundSchema = exports.ConversationAppendRequest$inboundSchema;
    /** @deprecated use `ConversationAppendRequest$outboundSchema` instead. */
    ConversationAppendRequest$.outboundSchema = exports.ConversationAppendRequest$outboundSchema;
})(ConversationAppendRequest$ || (exports.ConversationAppendRequest$ = ConversationAppendRequest$ = {}));
function conversationAppendRequestToJSON(conversationAppendRequest) {
    return JSON.stringify(exports.ConversationAppendRequest$outboundSchema.parse(conversationAppendRequest));
}
function conversationAppendRequestFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ConversationAppendRequest$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ConversationAppendRequest' from JSON`);
}
//# sourceMappingURL=conversationappendrequest.js.map