"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationRequest$ = exports.ConversationRequest$outboundSchema = exports.ConversationRequest$inboundSchema = exports.Tools$ = exports.Tools$outboundSchema = exports.Tools$inboundSchema = exports.HandoffExecution$ = exports.HandoffExecution$outboundSchema = exports.HandoffExecution$inboundSchema = exports.HandoffExecution = void 0;
exports.toolsToJSON = toolsToJSON;
exports.toolsFromJSON = toolsFromJSON;
exports.conversationRequestToJSON = conversationRequestToJSON;
exports.conversationRequestFromJSON = conversationRequestFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const codeinterpretertool_js_1 = require("./codeinterpretertool.js");
const completionargs_js_1 = require("./completionargs.js");
const conversationinputs_js_1 = require("./conversationinputs.js");
const documentlibrarytool_js_1 = require("./documentlibrarytool.js");
const functiontool_js_1 = require("./functiontool.js");
const imagegenerationtool_js_1 = require("./imagegenerationtool.js");
const websearchpremiumtool_js_1 = require("./websearchpremiumtool.js");
const websearchtool_js_1 = require("./websearchtool.js");
exports.HandoffExecution = {
    Client: "client",
    Server: "server",
};
/** @internal */
exports.HandoffExecution$inboundSchema = z.nativeEnum(exports.HandoffExecution);
/** @internal */
exports.HandoffExecution$outboundSchema = exports.HandoffExecution$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var HandoffExecution$;
(function (HandoffExecution$) {
    /** @deprecated use `HandoffExecution$inboundSchema` instead. */
    HandoffExecution$.inboundSchema = exports.HandoffExecution$inboundSchema;
    /** @deprecated use `HandoffExecution$outboundSchema` instead. */
    HandoffExecution$.outboundSchema = exports.HandoffExecution$outboundSchema;
})(HandoffExecution$ || (exports.HandoffExecution$ = HandoffExecution$ = {}));
/** @internal */
exports.Tools$inboundSchema = z
    .union([
    codeinterpretertool_js_1.CodeInterpreterTool$inboundSchema.and(z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
        type: v.type,
    }))),
    imagegenerationtool_js_1.ImageGenerationTool$inboundSchema.and(z.object({ type: z.literal("image_generation") }).transform((v) => ({
        type: v.type,
    }))),
    websearchtool_js_1.WebSearchTool$inboundSchema.and(z.object({ type: z.literal("web_search") }).transform((v) => ({
        type: v.type,
    }))),
    websearchpremiumtool_js_1.WebSearchPremiumTool$inboundSchema.and(z.object({ type: z.literal("web_search_premium") }).transform((v) => ({
        type: v.type,
    }))),
    documentlibrarytool_js_1.DocumentLibraryTool$inboundSchema.and(z.object({ type: z.literal("document_library") }).transform((v) => ({
        type: v.type,
    }))),
    functiontool_js_1.FunctionTool$inboundSchema.and(z.object({ type: z.literal("function") }).transform((v) => ({
        type: v.type,
    }))),
]);
/** @internal */
exports.Tools$outboundSchema = z.union([
    codeinterpretertool_js_1.CodeInterpreterTool$outboundSchema.and(z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
        type: v.type,
    }))),
    imagegenerationtool_js_1.ImageGenerationTool$outboundSchema.and(z.object({ type: z.literal("image_generation") }).transform((v) => ({
        type: v.type,
    }))),
    websearchtool_js_1.WebSearchTool$outboundSchema.and(z.object({ type: z.literal("web_search") }).transform((v) => ({
        type: v.type,
    }))),
    websearchpremiumtool_js_1.WebSearchPremiumTool$outboundSchema.and(z.object({ type: z.literal("web_search_premium") }).transform((v) => ({
        type: v.type,
    }))),
    documentlibrarytool_js_1.DocumentLibraryTool$outboundSchema.and(z.object({ type: z.literal("document_library") }).transform((v) => ({
        type: v.type,
    }))),
    functiontool_js_1.FunctionTool$outboundSchema.and(z.object({ type: z.literal("function") }).transform((v) => ({
        type: v.type,
    }))),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Tools$;
(function (Tools$) {
    /** @deprecated use `Tools$inboundSchema` instead. */
    Tools$.inboundSchema = exports.Tools$inboundSchema;
    /** @deprecated use `Tools$outboundSchema` instead. */
    Tools$.outboundSchema = exports.Tools$outboundSchema;
})(Tools$ || (exports.Tools$ = Tools$ = {}));
function toolsToJSON(tools) {
    return JSON.stringify(exports.Tools$outboundSchema.parse(tools));
}
function toolsFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.Tools$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'Tools' from JSON`);
}
/** @internal */
exports.ConversationRequest$inboundSchema = z.object({
    inputs: conversationinputs_js_1.ConversationInputs$inboundSchema,
    stream: z.boolean().default(false),
    store: z.nullable(z.boolean()).optional(),
    handoff_execution: z.nullable(exports.HandoffExecution$inboundSchema).optional(),
    instructions: z.nullable(z.string()).optional(),
    tools: z.nullable(z.array(z.union([
        codeinterpretertool_js_1.CodeInterpreterTool$inboundSchema.and(z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
            type: v.type,
        }))),
        imagegenerationtool_js_1.ImageGenerationTool$inboundSchema.and(z.object({ type: z.literal("image_generation") }).transform((v) => ({
            type: v.type,
        }))),
        websearchtool_js_1.WebSearchTool$inboundSchema.and(z.object({ type: z.literal("web_search") }).transform((v) => ({
            type: v.type,
        }))),
        websearchpremiumtool_js_1.WebSearchPremiumTool$inboundSchema.and(z.object({ type: z.literal("web_search_premium") }).transform((v) => ({ type: v.type }))),
        documentlibrarytool_js_1.DocumentLibraryTool$inboundSchema.and(z.object({ type: z.literal("document_library") }).transform((v) => ({
            type: v.type,
        }))),
        functiontool_js_1.FunctionTool$inboundSchema.and(z.object({ type: z.literal("function") }).transform((v) => ({
            type: v.type,
        }))),
    ]))).optional(),
    completion_args: z.nullable(completionargs_js_1.CompletionArgs$inboundSchema).optional(),
    name: z.nullable(z.string()).optional(),
    description: z.nullable(z.string()).optional(),
    agent_id: z.nullable(z.string()).optional(),
    model: z.nullable(z.string()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "handoff_execution": "handoffExecution",
        "completion_args": "completionArgs",
        "agent_id": "agentId",
    });
});
/** @internal */
exports.ConversationRequest$outboundSchema = z.object({
    inputs: conversationinputs_js_1.ConversationInputs$outboundSchema,
    stream: z.boolean().default(false),
    store: z.nullable(z.boolean()).optional(),
    handoffExecution: z.nullable(exports.HandoffExecution$outboundSchema).optional(),
    instructions: z.nullable(z.string()).optional(),
    tools: z.nullable(z.array(z.union([
        codeinterpretertool_js_1.CodeInterpreterTool$outboundSchema.and(z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
            type: v.type,
        }))),
        imagegenerationtool_js_1.ImageGenerationTool$outboundSchema.and(z.object({ type: z.literal("image_generation") }).transform((v) => ({
            type: v.type,
        }))),
        websearchtool_js_1.WebSearchTool$outboundSchema.and(z.object({ type: z.literal("web_search") }).transform((v) => ({
            type: v.type,
        }))),
        websearchpremiumtool_js_1.WebSearchPremiumTool$outboundSchema.and(z.object({ type: z.literal("web_search_premium") }).transform((v) => ({ type: v.type }))),
        documentlibrarytool_js_1.DocumentLibraryTool$outboundSchema.and(z.object({ type: z.literal("document_library") }).transform((v) => ({
            type: v.type,
        }))),
        functiontool_js_1.FunctionTool$outboundSchema.and(z.object({ type: z.literal("function") }).transform((v) => ({
            type: v.type,
        }))),
    ]))).optional(),
    completionArgs: z.nullable(completionargs_js_1.CompletionArgs$outboundSchema).optional(),
    name: z.nullable(z.string()).optional(),
    description: z.nullable(z.string()).optional(),
    agentId: z.nullable(z.string()).optional(),
    model: z.nullable(z.string()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        handoffExecution: "handoff_execution",
        completionArgs: "completion_args",
        agentId: "agent_id",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationRequest$;
(function (ConversationRequest$) {
    /** @deprecated use `ConversationRequest$inboundSchema` instead. */
    ConversationRequest$.inboundSchema = exports.ConversationRequest$inboundSchema;
    /** @deprecated use `ConversationRequest$outboundSchema` instead. */
    ConversationRequest$.outboundSchema = exports.ConversationRequest$outboundSchema;
})(ConversationRequest$ || (exports.ConversationRequest$ = ConversationRequest$ = {}));
function conversationRequestToJSON(conversationRequest) {
    return JSON.stringify(exports.ConversationRequest$outboundSchema.parse(conversationRequest));
}
function conversationRequestFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ConversationRequest$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ConversationRequest' from JSON`);
}
//# sourceMappingURL=conversationrequest.js.map