import * as z from "zod";
import { OpenEnum } from "../../types/enums.js";
export declare const MistralPromptMode: {
    readonly Reasoning: "reasoning";
};
export type MistralPromptMode = OpenEnum<typeof MistralPromptMode>;
/** @internal */
export declare const MistralPromptMode$inboundSchema: z.ZodType<MistralPromptMode, z.ZodTypeDef, unknown>;
/** @internal */
export declare const MistralPromptMode$outboundSchema: z.ZodType<MistralPromptMode, z.ZodTypeDef, MistralPromptMode>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace MistralPromptMode$ {
    /** @deprecated use `MistralPromptMode$inboundSchema` instead. */
    const inboundSchema: z.ZodType<MistralPromptMode, z.ZodTypeDef, unknown>;
    /** @deprecated use `MistralPromptMode$outboundSchema` instead. */
    const outboundSchema: z.ZodType<MistralPromptMode, z.ZodTypeDef, MistralPromptMode>;
}
//# sourceMappingURL=mistralpromptmode.d.ts.map