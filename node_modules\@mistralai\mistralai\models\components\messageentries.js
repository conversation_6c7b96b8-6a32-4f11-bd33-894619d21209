"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageEntries$ = exports.MessageEntries$outboundSchema = exports.MessageEntries$inboundSchema = void 0;
exports.messageEntriesToJSON = messageEntriesToJSON;
exports.messageEntriesFromJSON = messageEntriesFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const messageinputentry_js_1 = require("./messageinputentry.js");
const messageoutputentry_js_1 = require("./messageoutputentry.js");
/** @internal */
exports.MessageEntries$inboundSchema = z.union([
    messageinputentry_js_1.MessageInputEntry$inboundSchema,
    messageoutputentry_js_1.MessageOutputEntry$inboundSchema,
]);
/** @internal */
exports.MessageEntries$outboundSchema = z.union([
    messageinputentry_js_1.MessageInputEntry$outboundSchema,
    messageoutputentry_js_1.MessageOutputEntry$outboundSchema,
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MessageEntries$;
(function (MessageEntries$) {
    /** @deprecated use `MessageEntries$inboundSchema` instead. */
    MessageEntries$.inboundSchema = exports.MessageEntries$inboundSchema;
    /** @deprecated use `MessageEntries$outboundSchema` instead. */
    MessageEntries$.outboundSchema = exports.MessageEntries$outboundSchema;
})(MessageEntries$ || (exports.MessageEntries$ = MessageEntries$ = {}));
function messageEntriesToJSON(messageEntries) {
    return JSON.stringify(exports.MessageEntries$outboundSchema.parse(messageEntries));
}
function messageEntriesFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.MessageEntries$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'MessageEntries' from JSON`);
}
//# sourceMappingURL=messageentries.js.map