"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolExecutionDoneEvent$ = exports.ToolExecutionDoneEvent$outboundSchema = exports.ToolExecutionDoneEvent$inboundSchema = exports.ToolExecutionDoneEventType$ = exports.ToolExecutionDoneEventType$outboundSchema = exports.ToolExecutionDoneEventType$inboundSchema = exports.ToolExecutionDoneEventType = void 0;
exports.toolExecutionDoneEventToJSON = toolExecutionDoneEventToJSON;
exports.toolExecutionDoneEventFromJSON = toolExecutionDoneEventFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const builtinconnectors_js_1 = require("./builtinconnectors.js");
exports.ToolExecutionDoneEventType = {
    ToolExecutionDone: "tool.execution.done",
};
/** @internal */
exports.ToolExecutionDoneEventType$inboundSchema = z.nativeEnum(exports.ToolExecutionDoneEventType);
/** @internal */
exports.ToolExecutionDoneEventType$outboundSchema = exports.ToolExecutionDoneEventType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ToolExecutionDoneEventType$;
(function (ToolExecutionDoneEventType$) {
    /** @deprecated use `ToolExecutionDoneEventType$inboundSchema` instead. */
    ToolExecutionDoneEventType$.inboundSchema = exports.ToolExecutionDoneEventType$inboundSchema;
    /** @deprecated use `ToolExecutionDoneEventType$outboundSchema` instead. */
    ToolExecutionDoneEventType$.outboundSchema = exports.ToolExecutionDoneEventType$outboundSchema;
})(ToolExecutionDoneEventType$ || (exports.ToolExecutionDoneEventType$ = ToolExecutionDoneEventType$ = {}));
/** @internal */
exports.ToolExecutionDoneEvent$inboundSchema = z.object({
    type: exports.ToolExecutionDoneEventType$inboundSchema.default("tool.execution.done"),
    created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
        .optional(),
    output_index: z.number().int().default(0),
    id: z.string(),
    name: builtinconnectors_js_1.BuiltInConnectors$inboundSchema,
    info: z.record(z.any()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "created_at": "createdAt",
        "output_index": "outputIndex",
    });
});
/** @internal */
exports.ToolExecutionDoneEvent$outboundSchema = z.object({
    type: exports.ToolExecutionDoneEventType$outboundSchema.default("tool.execution.done"),
    createdAt: z.date().transform(v => v.toISOString()).optional(),
    outputIndex: z.number().int().default(0),
    id: z.string(),
    name: builtinconnectors_js_1.BuiltInConnectors$outboundSchema,
    info: z.record(z.any()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        createdAt: "created_at",
        outputIndex: "output_index",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ToolExecutionDoneEvent$;
(function (ToolExecutionDoneEvent$) {
    /** @deprecated use `ToolExecutionDoneEvent$inboundSchema` instead. */
    ToolExecutionDoneEvent$.inboundSchema = exports.ToolExecutionDoneEvent$inboundSchema;
    /** @deprecated use `ToolExecutionDoneEvent$outboundSchema` instead. */
    ToolExecutionDoneEvent$.outboundSchema = exports.ToolExecutionDoneEvent$outboundSchema;
})(ToolExecutionDoneEvent$ || (exports.ToolExecutionDoneEvent$ = ToolExecutionDoneEvent$ = {}));
function toolExecutionDoneEventToJSON(toolExecutionDoneEvent) {
    return JSON.stringify(exports.ToolExecutionDoneEvent$outboundSchema.parse(toolExecutionDoneEvent));
}
function toolExecutionDoneEventFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ToolExecutionDoneEvent$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ToolExecutionDoneEvent' from JSON`);
}
//# sourceMappingURL=toolexecutiondoneevent.js.map