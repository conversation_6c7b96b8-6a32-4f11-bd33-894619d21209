"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompletionFTModelOut$ = exports.CompletionFTModelOut$outboundSchema = exports.CompletionFTModelOut$inboundSchema = exports.ModelType$ = exports.ModelType$outboundSchema = exports.ModelType$inboundSchema = exports.CompletionFTModelOutObject$ = exports.CompletionFTModelOutObject$outboundSchema = exports.CompletionFTModelOutObject$inboundSchema = exports.ModelType = exports.CompletionFTModelOutObject = void 0;
exports.completionFTModelOutToJSON = completionFTModelOutToJSON;
exports.completionFTModelOutFromJSON = completionFTModelOutFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const ftmodelcapabilitiesout_js_1 = require("./ftmodelcapabilitiesout.js");
exports.CompletionFTModelOutObject = {
    Model: "model",
};
exports.ModelType = {
    Completion: "completion",
};
/** @internal */
exports.CompletionFTModelOutObject$inboundSchema = z.nativeEnum(exports.CompletionFTModelOutObject);
/** @internal */
exports.CompletionFTModelOutObject$outboundSchema = exports.CompletionFTModelOutObject$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var CompletionFTModelOutObject$;
(function (CompletionFTModelOutObject$) {
    /** @deprecated use `CompletionFTModelOutObject$inboundSchema` instead. */
    CompletionFTModelOutObject$.inboundSchema = exports.CompletionFTModelOutObject$inboundSchema;
    /** @deprecated use `CompletionFTModelOutObject$outboundSchema` instead. */
    CompletionFTModelOutObject$.outboundSchema = exports.CompletionFTModelOutObject$outboundSchema;
})(CompletionFTModelOutObject$ || (exports.CompletionFTModelOutObject$ = CompletionFTModelOutObject$ = {}));
/** @internal */
exports.ModelType$inboundSchema = z
    .nativeEnum(exports.ModelType);
/** @internal */
exports.ModelType$outboundSchema = exports.ModelType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ModelType$;
(function (ModelType$) {
    /** @deprecated use `ModelType$inboundSchema` instead. */
    ModelType$.inboundSchema = exports.ModelType$inboundSchema;
    /** @deprecated use `ModelType$outboundSchema` instead. */
    ModelType$.outboundSchema = exports.ModelType$outboundSchema;
})(ModelType$ || (exports.ModelType$ = ModelType$ = {}));
/** @internal */
exports.CompletionFTModelOut$inboundSchema = z.object({
    id: z.string(),
    object: exports.CompletionFTModelOutObject$inboundSchema.default("model"),
    created: z.number().int(),
    owned_by: z.string(),
    root: z.string(),
    archived: z.boolean(),
    name: z.nullable(z.string()).optional(),
    description: z.nullable(z.string()).optional(),
    capabilities: ftmodelcapabilitiesout_js_1.FTModelCapabilitiesOut$inboundSchema,
    max_context_length: z.number().int().default(32768),
    aliases: z.array(z.string()).optional(),
    job: z.string(),
    model_type: exports.ModelType$inboundSchema.default("completion"),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "owned_by": "ownedBy",
        "max_context_length": "maxContextLength",
        "model_type": "modelType",
    });
});
/** @internal */
exports.CompletionFTModelOut$outboundSchema = z.object({
    id: z.string(),
    object: exports.CompletionFTModelOutObject$outboundSchema.default("model"),
    created: z.number().int(),
    ownedBy: z.string(),
    root: z.string(),
    archived: z.boolean(),
    name: z.nullable(z.string()).optional(),
    description: z.nullable(z.string()).optional(),
    capabilities: ftmodelcapabilitiesout_js_1.FTModelCapabilitiesOut$outboundSchema,
    maxContextLength: z.number().int().default(32768),
    aliases: z.array(z.string()).optional(),
    job: z.string(),
    modelType: exports.ModelType$outboundSchema.default("completion"),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        ownedBy: "owned_by",
        maxContextLength: "max_context_length",
        modelType: "model_type",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var CompletionFTModelOut$;
(function (CompletionFTModelOut$) {
    /** @deprecated use `CompletionFTModelOut$inboundSchema` instead. */
    CompletionFTModelOut$.inboundSchema = exports.CompletionFTModelOut$inboundSchema;
    /** @deprecated use `CompletionFTModelOut$outboundSchema` instead. */
    CompletionFTModelOut$.outboundSchema = exports.CompletionFTModelOut$outboundSchema;
})(CompletionFTModelOut$ || (exports.CompletionFTModelOut$ = CompletionFTModelOut$ = {}));
function completionFTModelOutToJSON(completionFTModelOut) {
    return JSON.stringify(exports.CompletionFTModelOut$outboundSchema.parse(completionFTModelOut));
}
function completionFTModelOutFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.CompletionFTModelOut$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'CompletionFTModelOut' from JSON`);
}
//# sourceMappingURL=completionftmodelout.js.map