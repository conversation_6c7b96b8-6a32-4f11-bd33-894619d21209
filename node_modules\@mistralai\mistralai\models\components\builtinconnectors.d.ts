import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";
export declare const BuiltInConnectors: {
    readonly WebSearch: "web_search";
    readonly WebSearchPremium: "web_search_premium";
    readonly CodeInterpreter: "code_interpreter";
    readonly ImageGeneration: "image_generation";
    readonly DocumentLibrary: "document_library";
};
export type BuiltInConnectors = ClosedEnum<typeof BuiltInConnectors>;
/** @internal */
export declare const BuiltInConnectors$inboundSchema: z.ZodNativeEnum<typeof BuiltInConnectors>;
/** @internal */
export declare const BuiltInConnectors$outboundSchema: z.ZodNativeEnum<typeof BuiltInConnectors>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace BuiltInConnectors$ {
    /** @deprecated use `BuiltInConnectors$inboundSchema` instead. */
    const inboundSchema: z.ZodNativeEnum<{
        readonly WebSearch: "web_search";
        readonly WebSearchPremium: "web_search_premium";
        readonly CodeInterpreter: "code_interpreter";
        readonly ImageGeneration: "image_generation";
        readonly DocumentLibrary: "document_library";
    }>;
    /** @deprecated use `BuiltInConnectors$outboundSchema` instead. */
    const outboundSchema: z.ZodNativeEnum<{
        readonly WebSearch: "web_search";
        readonly WebSearchPremium: "web_search_premium";
        readonly CodeInterpreter: "code_interpreter";
        readonly ImageGeneration: "image_generation";
        readonly DocumentLibrary: "document_library";
    }>;
}
//# sourceMappingURL=builtinconnectors.d.ts.map