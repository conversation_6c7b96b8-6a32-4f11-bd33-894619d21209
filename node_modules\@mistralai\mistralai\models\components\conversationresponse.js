"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationResponse$ = exports.ConversationResponse$outboundSchema = exports.ConversationResponse$inboundSchema = exports.Outputs$ = exports.Outputs$outboundSchema = exports.Outputs$inboundSchema = exports.ConversationResponseObject$ = exports.ConversationResponseObject$outboundSchema = exports.ConversationResponseObject$inboundSchema = exports.ConversationResponseObject = void 0;
exports.outputsToJSON = outputsToJSON;
exports.outputsFromJSON = outputsFromJSON;
exports.conversationResponseToJSON = conversationResponseToJSON;
exports.conversationResponseFromJSON = conversationResponseFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const agenthandoffentry_js_1 = require("./agenthandoffentry.js");
const conversationusageinfo_js_1 = require("./conversationusageinfo.js");
const functioncallentry_js_1 = require("./functioncallentry.js");
const messageoutputentry_js_1 = require("./messageoutputentry.js");
const toolexecutionentry_js_1 = require("./toolexecutionentry.js");
exports.ConversationResponseObject = {
    ConversationResponse: "conversation.response",
};
/** @internal */
exports.ConversationResponseObject$inboundSchema = z.nativeEnum(exports.ConversationResponseObject);
/** @internal */
exports.ConversationResponseObject$outboundSchema = exports.ConversationResponseObject$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationResponseObject$;
(function (ConversationResponseObject$) {
    /** @deprecated use `ConversationResponseObject$inboundSchema` instead. */
    ConversationResponseObject$.inboundSchema = exports.ConversationResponseObject$inboundSchema;
    /** @deprecated use `ConversationResponseObject$outboundSchema` instead. */
    ConversationResponseObject$.outboundSchema = exports.ConversationResponseObject$outboundSchema;
})(ConversationResponseObject$ || (exports.ConversationResponseObject$ = ConversationResponseObject$ = {}));
/** @internal */
exports.Outputs$inboundSchema = z.union([
    toolexecutionentry_js_1.ToolExecutionEntry$inboundSchema,
    functioncallentry_js_1.FunctionCallEntry$inboundSchema,
    messageoutputentry_js_1.MessageOutputEntry$inboundSchema,
    agenthandoffentry_js_1.AgentHandoffEntry$inboundSchema,
]);
/** @internal */
exports.Outputs$outboundSchema = z.union([
    toolexecutionentry_js_1.ToolExecutionEntry$outboundSchema,
    functioncallentry_js_1.FunctionCallEntry$outboundSchema,
    messageoutputentry_js_1.MessageOutputEntry$outboundSchema,
    agenthandoffentry_js_1.AgentHandoffEntry$outboundSchema,
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Outputs$;
(function (Outputs$) {
    /** @deprecated use `Outputs$inboundSchema` instead. */
    Outputs$.inboundSchema = exports.Outputs$inboundSchema;
    /** @deprecated use `Outputs$outboundSchema` instead. */
    Outputs$.outboundSchema = exports.Outputs$outboundSchema;
})(Outputs$ || (exports.Outputs$ = Outputs$ = {}));
function outputsToJSON(outputs) {
    return JSON.stringify(exports.Outputs$outboundSchema.parse(outputs));
}
function outputsFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.Outputs$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'Outputs' from JSON`);
}
/** @internal */
exports.ConversationResponse$inboundSchema = z.object({
    object: exports.ConversationResponseObject$inboundSchema.default("conversation.response"),
    conversation_id: z.string(),
    outputs: z.array(z.union([
        toolexecutionentry_js_1.ToolExecutionEntry$inboundSchema,
        functioncallentry_js_1.FunctionCallEntry$inboundSchema,
        messageoutputentry_js_1.MessageOutputEntry$inboundSchema,
        agenthandoffentry_js_1.AgentHandoffEntry$inboundSchema,
    ])),
    usage: conversationusageinfo_js_1.ConversationUsageInfo$inboundSchema,
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "conversation_id": "conversationId",
    });
});
/** @internal */
exports.ConversationResponse$outboundSchema = z.object({
    object: exports.ConversationResponseObject$outboundSchema.default("conversation.response"),
    conversationId: z.string(),
    outputs: z.array(z.union([
        toolexecutionentry_js_1.ToolExecutionEntry$outboundSchema,
        functioncallentry_js_1.FunctionCallEntry$outboundSchema,
        messageoutputentry_js_1.MessageOutputEntry$outboundSchema,
        agenthandoffentry_js_1.AgentHandoffEntry$outboundSchema,
    ])),
    usage: conversationusageinfo_js_1.ConversationUsageInfo$outboundSchema,
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        conversationId: "conversation_id",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationResponse$;
(function (ConversationResponse$) {
    /** @deprecated use `ConversationResponse$inboundSchema` instead. */
    ConversationResponse$.inboundSchema = exports.ConversationResponse$inboundSchema;
    /** @deprecated use `ConversationResponse$outboundSchema` instead. */
    ConversationResponse$.outboundSchema = exports.ConversationResponse$outboundSchema;
})(ConversationResponse$ || (exports.ConversationResponse$ = ConversationResponse$ = {}));
function conversationResponseToJSON(conversationResponse) {
    return JSON.stringify(exports.ConversationResponse$outboundSchema.parse(conversationResponse));
}
function conversationResponseFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ConversationResponse$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ConversationResponse' from JSON`);
}
//# sourceMappingURL=conversationresponse.js.map