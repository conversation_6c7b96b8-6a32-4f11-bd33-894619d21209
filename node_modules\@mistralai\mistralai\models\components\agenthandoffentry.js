"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentHandoffEntry$ = exports.AgentHandoffEntry$outboundSchema = exports.AgentHandoffEntry$inboundSchema = exports.AgentHandoffEntryType$ = exports.AgentHandoffEntryType$outboundSchema = exports.AgentHandoffEntryType$inboundSchema = exports.AgentHandoffEntryObject$ = exports.AgentHandoffEntryObject$outboundSchema = exports.AgentHandoffEntryObject$inboundSchema = exports.AgentHandoffEntryType = exports.AgentHandoffEntryObject = void 0;
exports.agentHandoffEntryToJSON = agentHandoffEntryToJSON;
exports.agentHandoffEntryFromJSON = agentHandoffEntryFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
exports.AgentHandoffEntryObject = {
    Entry: "entry",
};
exports.AgentHandoffEntryType = {
    AgentHandoff: "agent.handoff",
};
/** @internal */
exports.AgentHandoffEntryObject$inboundSchema = z.nativeEnum(exports.AgentHandoffEntryObject);
/** @internal */
exports.AgentHandoffEntryObject$outboundSchema = exports.AgentHandoffEntryObject$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentHandoffEntryObject$;
(function (AgentHandoffEntryObject$) {
    /** @deprecated use `AgentHandoffEntryObject$inboundSchema` instead. */
    AgentHandoffEntryObject$.inboundSchema = exports.AgentHandoffEntryObject$inboundSchema;
    /** @deprecated use `AgentHandoffEntryObject$outboundSchema` instead. */
    AgentHandoffEntryObject$.outboundSchema = exports.AgentHandoffEntryObject$outboundSchema;
})(AgentHandoffEntryObject$ || (exports.AgentHandoffEntryObject$ = AgentHandoffEntryObject$ = {}));
/** @internal */
exports.AgentHandoffEntryType$inboundSchema = z.nativeEnum(exports.AgentHandoffEntryType);
/** @internal */
exports.AgentHandoffEntryType$outboundSchema = exports.AgentHandoffEntryType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentHandoffEntryType$;
(function (AgentHandoffEntryType$) {
    /** @deprecated use `AgentHandoffEntryType$inboundSchema` instead. */
    AgentHandoffEntryType$.inboundSchema = exports.AgentHandoffEntryType$inboundSchema;
    /** @deprecated use `AgentHandoffEntryType$outboundSchema` instead. */
    AgentHandoffEntryType$.outboundSchema = exports.AgentHandoffEntryType$outboundSchema;
})(AgentHandoffEntryType$ || (exports.AgentHandoffEntryType$ = AgentHandoffEntryType$ = {}));
/** @internal */
exports.AgentHandoffEntry$inboundSchema = z.object({
    object: exports.AgentHandoffEntryObject$inboundSchema.default("entry"),
    type: exports.AgentHandoffEntryType$inboundSchema.default("agent.handoff"),
    created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
        .optional(),
    completed_at: z.nullable(z.string().datetime({ offset: true }).transform(v => new Date(v))).optional(),
    id: z.string().optional(),
    previous_agent_id: z.string(),
    previous_agent_name: z.string(),
    next_agent_id: z.string(),
    next_agent_name: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "created_at": "createdAt",
        "completed_at": "completedAt",
        "previous_agent_id": "previousAgentId",
        "previous_agent_name": "previousAgentName",
        "next_agent_id": "nextAgentId",
        "next_agent_name": "nextAgentName",
    });
});
/** @internal */
exports.AgentHandoffEntry$outboundSchema = z.object({
    object: exports.AgentHandoffEntryObject$outboundSchema.default("entry"),
    type: exports.AgentHandoffEntryType$outboundSchema.default("agent.handoff"),
    createdAt: z.date().transform(v => v.toISOString()).optional(),
    completedAt: z.nullable(z.date().transform(v => v.toISOString())).optional(),
    id: z.string().optional(),
    previousAgentId: z.string(),
    previousAgentName: z.string(),
    nextAgentId: z.string(),
    nextAgentName: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        createdAt: "created_at",
        completedAt: "completed_at",
        previousAgentId: "previous_agent_id",
        previousAgentName: "previous_agent_name",
        nextAgentId: "next_agent_id",
        nextAgentName: "next_agent_name",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentHandoffEntry$;
(function (AgentHandoffEntry$) {
    /** @deprecated use `AgentHandoffEntry$inboundSchema` instead. */
    AgentHandoffEntry$.inboundSchema = exports.AgentHandoffEntry$inboundSchema;
    /** @deprecated use `AgentHandoffEntry$outboundSchema` instead. */
    AgentHandoffEntry$.outboundSchema = exports.AgentHandoffEntry$outboundSchema;
})(AgentHandoffEntry$ || (exports.AgentHandoffEntry$ = AgentHandoffEntry$ = {}));
function agentHandoffEntryToJSON(agentHandoffEntry) {
    return JSON.stringify(exports.AgentHandoffEntry$outboundSchema.parse(agentHandoffEntry));
}
function agentHandoffEntryFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.AgentHandoffEntry$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'AgentHandoffEntry' from JSON`);
}
//# sourceMappingURL=agenthandoffentry.js.map