"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FunctionCallEntry$ = exports.FunctionCallEntry$outboundSchema = exports.FunctionCallEntry$inboundSchema = exports.FunctionCallEntryType$ = exports.FunctionCallEntryType$outboundSchema = exports.FunctionCallEntryType$inboundSchema = exports.FunctionCallEntryObject$ = exports.FunctionCallEntryObject$outboundSchema = exports.FunctionCallEntryObject$inboundSchema = exports.FunctionCallEntryType = exports.FunctionCallEntryObject = void 0;
exports.functionCallEntryToJSON = functionCallEntryToJSON;
exports.functionCallEntryFromJSON = functionCallEntryFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const functioncallentryarguments_js_1 = require("./functioncallentryarguments.js");
exports.FunctionCallEntryObject = {
    Entry: "entry",
};
exports.FunctionCallEntryType = {
    FunctionCall: "function.call",
};
/** @internal */
exports.FunctionCallEntryObject$inboundSchema = z.nativeEnum(exports.FunctionCallEntryObject);
/** @internal */
exports.FunctionCallEntryObject$outboundSchema = exports.FunctionCallEntryObject$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FunctionCallEntryObject$;
(function (FunctionCallEntryObject$) {
    /** @deprecated use `FunctionCallEntryObject$inboundSchema` instead. */
    FunctionCallEntryObject$.inboundSchema = exports.FunctionCallEntryObject$inboundSchema;
    /** @deprecated use `FunctionCallEntryObject$outboundSchema` instead. */
    FunctionCallEntryObject$.outboundSchema = exports.FunctionCallEntryObject$outboundSchema;
})(FunctionCallEntryObject$ || (exports.FunctionCallEntryObject$ = FunctionCallEntryObject$ = {}));
/** @internal */
exports.FunctionCallEntryType$inboundSchema = z.nativeEnum(exports.FunctionCallEntryType);
/** @internal */
exports.FunctionCallEntryType$outboundSchema = exports.FunctionCallEntryType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FunctionCallEntryType$;
(function (FunctionCallEntryType$) {
    /** @deprecated use `FunctionCallEntryType$inboundSchema` instead. */
    FunctionCallEntryType$.inboundSchema = exports.FunctionCallEntryType$inboundSchema;
    /** @deprecated use `FunctionCallEntryType$outboundSchema` instead. */
    FunctionCallEntryType$.outboundSchema = exports.FunctionCallEntryType$outboundSchema;
})(FunctionCallEntryType$ || (exports.FunctionCallEntryType$ = FunctionCallEntryType$ = {}));
/** @internal */
exports.FunctionCallEntry$inboundSchema = z.object({
    object: exports.FunctionCallEntryObject$inboundSchema.default("entry"),
    type: exports.FunctionCallEntryType$inboundSchema.default("function.call"),
    created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
        .optional(),
    completed_at: z.nullable(z.string().datetime({ offset: true }).transform(v => new Date(v))).optional(),
    id: z.string().optional(),
    tool_call_id: z.string(),
    name: z.string(),
    arguments: functioncallentryarguments_js_1.FunctionCallEntryArguments$inboundSchema,
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "created_at": "createdAt",
        "completed_at": "completedAt",
        "tool_call_id": "toolCallId",
    });
});
/** @internal */
exports.FunctionCallEntry$outboundSchema = z.object({
    object: exports.FunctionCallEntryObject$outboundSchema.default("entry"),
    type: exports.FunctionCallEntryType$outboundSchema.default("function.call"),
    createdAt: z.date().transform(v => v.toISOString()).optional(),
    completedAt: z.nullable(z.date().transform(v => v.toISOString())).optional(),
    id: z.string().optional(),
    toolCallId: z.string(),
    name: z.string(),
    arguments: functioncallentryarguments_js_1.FunctionCallEntryArguments$outboundSchema,
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        createdAt: "created_at",
        completedAt: "completed_at",
        toolCallId: "tool_call_id",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FunctionCallEntry$;
(function (FunctionCallEntry$) {
    /** @deprecated use `FunctionCallEntry$inboundSchema` instead. */
    FunctionCallEntry$.inboundSchema = exports.FunctionCallEntry$inboundSchema;
    /** @deprecated use `FunctionCallEntry$outboundSchema` instead. */
    FunctionCallEntry$.outboundSchema = exports.FunctionCallEntry$outboundSchema;
})(FunctionCallEntry$ || (exports.FunctionCallEntry$ = FunctionCallEntry$ = {}));
function functionCallEntryToJSON(functionCallEntry) {
    return JSON.stringify(exports.FunctionCallEntry$outboundSchema.parse(functionCallEntry));
}
function functionCallEntryFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.FunctionCallEntry$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'FunctionCallEntry' from JSON`);
}
//# sourceMappingURL=functioncallentry.js.map