{"version": 3, "file": "security.d.ts", "sourceRoot": "", "sources": ["../src/lib/security.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,UAAU,MAAM,+BAA+B,CAAC;AAE5D,KAAK,kBAAkB,GAAG;IACxB,QAAQ,EAAE,MAAM,CAAC;IACjB,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC9B,QAAQ,EAAE,MAAM,CAAC;IACjB,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAClC,QAAQ,EAAE,MAAM,CAAC;CAClB,CAAC;AAEF,oBAAY,iBAAiB;IAC3B,UAAU,eAAe;IACzB,wBAAwB,+BAA+B;CACxD;AAED,qBAAa,aAAc,SAAQ,KAAK;IAE7B,IAAI,EAAE,iBAAiB;gBAAvB,IAAI,EAAE,iBAAiB,EAC9B,OAAO,EAAE,MAAM;IAMjB,MAAM,CAAC,UAAU,IAAI,aAAa;IAMlC,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,GAAG,aAAa;CAMrD;AAED,MAAM,MAAM,aAAa,GAAG;IAC1B,KAAK,EAAE;QAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;QAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,CAAC;IACxE,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAChC,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACpC,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAChC,MAAM,EAAE,CAAC;QAAE,IAAI,EAAE,UAAU,CAAA;KAAE,GAAG,kBAAkB,CAAC,GAAG;QAAE,IAAI,EAAE,MAAM,CAAA;KAAE,CAAC;CACxE,CAAC;AAEF,KAAK,kBAAkB,GAAG;IACxB,IAAI,EAAE,YAAY,CAAC;IACnB,KAAK,EACD;QAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;QAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,GAChE,IAAI,GACJ,SAAS,CAAC;CACf,CAAC;AAEF,KAAK,mBAAmB,GAAG;IACzB,IAAI,EAAE,aAAa,CAAC;IACpB,KAAK,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC,SAAS,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF,KAAK,mBAAmB,GAAG;IACzB,IAAI,EAAE,eAAe,GAAG,cAAc,GAAG,eAAe,CAAC;IACzD,KAAK,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC,SAAS,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF,KAAK,iBAAiB,GAAG;IACvB,IAAI,EAAE,eAAe,CAAC;IACtB,KAAK,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC,SAAS,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF,KAAK,mBAAmB,GAAG;IACzB,IAAI,EAAE,QAAQ,CAAC;IACf,KAAK,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC,SAAS,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF,KAAK,oCAAoC,GAAG;IAC1C,IAAI,EAAE,2BAA2B,CAAC;IAClC,KAAK,EACD;QAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;QAAC,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,GACpE,IAAI,GACJ,SAAS,CAAC;CACf,CAAC;AAEF,KAAK,sCAAsC,GAAG;IAC5C,IAAI,EAAE,iBAAiB,CAAC;IACxB,KAAK,EACD,MAAM,GACN,IAAI,GACJ,SAAS,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF,KAAK,mBAAmB,GAAG;IACzB,IAAI,EAAE,aAAa,CAAC;IACpB,KAAK,EAAE,GAAG,GAAG,IAAI,GAAG,SAAS,CAAC;IAC9B,SAAS,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF,MAAM,MAAM,aAAa,GACrB,kBAAkB,GAClB,mBAAmB,GACnB,mBAAmB,GACnB,mBAAmB,GACnB,oCAAoC,GACpC,sCAAsC,GACtC,iBAAiB,GACjB,mBAAmB,CAAC;AAExB,wBAAgB,eAAe,CAC7B,GAAG,OAAO,EAAE,aAAa,EAAE,EAAE,GAC5B,aAAa,GAAG,IAAI,CAgFtB;AAiCD,wBAAgB,qBAAqB,CACnC,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,IAAI,GAAG,SAAS,GACxD,aAAa,GAAG,IAAI,CAUtB;AAED,wBAAsB,eAAe,CACnC,CAAC,SAAS,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC1C,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,OAAO,CAAC,CAAC,GAAG,SAAS,CAAC,CAMjE"}