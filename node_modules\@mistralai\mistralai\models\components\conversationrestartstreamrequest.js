"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationRestartStreamRequest$ = exports.ConversationRestartStreamRequest$outboundSchema = exports.ConversationRestartStreamRequest$inboundSchema = exports.ConversationRestartStreamRequestHandoffExecution$ = exports.ConversationRestartStreamRequestHandoffExecution$outboundSchema = exports.ConversationRestartStreamRequestHandoffExecution$inboundSchema = exports.ConversationRestartStreamRequestHandoffExecution = void 0;
exports.conversationRestartStreamRequestToJSON = conversationRestartStreamRequestToJSON;
exports.conversationRestartStreamRequestFromJSON = conversationRestartStreamRequestFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const completionargs_js_1 = require("./completionargs.js");
const conversationinputs_js_1 = require("./conversationinputs.js");
exports.ConversationRestartStreamRequestHandoffExecution = {
    Client: "client",
    Server: "server",
};
/** @internal */
exports.ConversationRestartStreamRequestHandoffExecution$inboundSchema = z
    .nativeEnum(exports.ConversationRestartStreamRequestHandoffExecution);
/** @internal */
exports.ConversationRestartStreamRequestHandoffExecution$outboundSchema = exports.ConversationRestartStreamRequestHandoffExecution$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationRestartStreamRequestHandoffExecution$;
(function (ConversationRestartStreamRequestHandoffExecution$) {
    /** @deprecated use `ConversationRestartStreamRequestHandoffExecution$inboundSchema` instead. */
    ConversationRestartStreamRequestHandoffExecution$.inboundSchema = exports.ConversationRestartStreamRequestHandoffExecution$inboundSchema;
    /** @deprecated use `ConversationRestartStreamRequestHandoffExecution$outboundSchema` instead. */
    ConversationRestartStreamRequestHandoffExecution$.outboundSchema = exports.ConversationRestartStreamRequestHandoffExecution$outboundSchema;
})(ConversationRestartStreamRequestHandoffExecution$ || (exports.ConversationRestartStreamRequestHandoffExecution$ = ConversationRestartStreamRequestHandoffExecution$ = {}));
/** @internal */
exports.ConversationRestartStreamRequest$inboundSchema = z.object({
    inputs: conversationinputs_js_1.ConversationInputs$inboundSchema,
    stream: z.boolean().default(true),
    store: z.boolean().default(true),
    handoff_execution: exports.ConversationRestartStreamRequestHandoffExecution$inboundSchema.default("server"),
    from_entry_id: z.string(),
    completion_args: completionargs_js_1.CompletionArgs$inboundSchema.optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "handoff_execution": "handoffExecution",
        "from_entry_id": "fromEntryId",
        "completion_args": "completionArgs",
    });
});
/** @internal */
exports.ConversationRestartStreamRequest$outboundSchema = z.object({
    inputs: conversationinputs_js_1.ConversationInputs$outboundSchema,
    stream: z.boolean().default(true),
    store: z.boolean().default(true),
    handoffExecution: exports.ConversationRestartStreamRequestHandoffExecution$outboundSchema.default("server"),
    fromEntryId: z.string(),
    completionArgs: completionargs_js_1.CompletionArgs$outboundSchema.optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        handoffExecution: "handoff_execution",
        fromEntryId: "from_entry_id",
        completionArgs: "completion_args",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationRestartStreamRequest$;
(function (ConversationRestartStreamRequest$) {
    /** @deprecated use `ConversationRestartStreamRequest$inboundSchema` instead. */
    ConversationRestartStreamRequest$.inboundSchema = exports.ConversationRestartStreamRequest$inboundSchema;
    /** @deprecated use `ConversationRestartStreamRequest$outboundSchema` instead. */
    ConversationRestartStreamRequest$.outboundSchema = exports.ConversationRestartStreamRequest$outboundSchema;
})(ConversationRestartStreamRequest$ || (exports.ConversationRestartStreamRequest$ = ConversationRestartStreamRequest$ = {}));
function conversationRestartStreamRequestToJSON(conversationRestartStreamRequest) {
    return JSON.stringify(exports.ConversationRestartStreamRequest$outboundSchema.parse(conversationRestartStreamRequest));
}
function conversationRestartStreamRequestFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ConversationRestartStreamRequest$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ConversationRestartStreamRequest' from JSON`);
}
//# sourceMappingURL=conversationrestartstreamrequest.js.map