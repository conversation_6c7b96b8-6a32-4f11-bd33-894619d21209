const express = require('express');
const { Mistral } = require('@mistralai/mistralai');
const OpenAI = require('openai');
const { GoogleGenerativeAI } = require('@google/generative-ai');

const app = express();

// Add CORS middleware to allow cross-origin requests
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }
  next();
});

app.use(express.json());

const mistral = new Mistral({ apiKey: process.env.MISTRAL_API_KEY || '' });
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY || '', baseURL: process.env.OPENAI_BASE_URL || 'https://openrouter.ai/api/v1' });
const gemini = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');

// Helper to select provider
function getProvider(provider) {
  switch ((provider || '').toLowerCase()) {
    case 'mistral':
      return { name: 'mistral', client: mistral, defaultModel: 'mistral-large-latest' };
    case 'openrouter':
      return { name: 'openrouter', client: openai, defaultModel: 'deepseek/deepseek-chat-v3-0324:free' }; // Changed default model for OpenRouter
    case 'gemini':
      return { name: 'gemini', client: gemini, defaultModel: 'gemini-pro' };
    default:
      return { name: 'mistral', client: mistral, defaultModel: 'mistral-large-latest' };
  }
}

app.post('/chat', async (req, res) => {
  const { messages, provider, model } = req.body; // Destructure model from req.body
  const { name, client, defaultModel } = getProvider(provider);
  try {
    let result;
    if (name === 'mistral') {
      result = await client.chat.complete({
        model: model || defaultModel,
        messages,
      });
      res.json({ provider: name, result });
    } else if (name === 'openrouter') {
      result = await client.chat.completions.create({
        model: model || defaultModel,
        messages,
      });
      res.json({ provider: name, result });
    } else if (name === 'gemini') {
      const geminiModel = client.getGenerativeModel({ model: model || defaultModel });
      const chat = geminiModel.startChat({
        history: messages.slice(0, -1).map(msg => ({
          role: msg.role === 'user' ? 'user' : 'model',
          parts: [{ text: msg.content }],
        })),
      });
      const lastMessage = messages[messages.length - 1];
      const geminiRes = await chat.sendMessage(lastMessage.content);
      result = geminiRes.response.text();
      res.json({ provider: name, result });
    } else {
      res.status(400).json({ error: 'Unknown provider' });
    }
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});