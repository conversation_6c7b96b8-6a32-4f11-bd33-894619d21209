"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Security$ = exports.Security$outboundSchema = exports.Security$inboundSchema = void 0;
exports.securityToJSON = securityToJSON;
exports.securityFromJSON = securityFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.Security$inboundSchema = z.object({
    ApiKey: z.string().optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "ApiKey": "apiKey",
    });
});
/** @internal */
exports.Security$outboundSchema = z.object({
    apiKey: z.string().optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        apiKey: "ApiKey",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Security$;
(function (Security$) {
    /** @deprecated use `Security$inboundSchema` instead. */
    Security$.inboundSchema = exports.Security$inboundSchema;
    /** @deprecated use `Security$outboundSchema` instead. */
    Security$.outboundSchema = exports.Security$outboundSchema;
})(Security$ || (exports.Security$ = Security$ = {}));
function securityToJSON(security) {
    return JSON.stringify(exports.Security$outboundSchema.parse(security));
}
function securityFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.Security$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'Security' from JSON`);
}
//# sourceMappingURL=security.js.map