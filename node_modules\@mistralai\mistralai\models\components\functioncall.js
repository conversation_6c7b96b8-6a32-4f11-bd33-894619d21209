"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FunctionCall$ = exports.FunctionCall$outboundSchema = exports.FunctionCall$inboundSchema = exports.Arguments$ = exports.Arguments$outboundSchema = exports.Arguments$inboundSchema = void 0;
exports.argumentsToJSON = argumentsToJSON;
exports.argumentsFromJSON = argumentsFromJSON;
exports.functionCallToJSON = functionCallToJSON;
exports.functionCallFromJSON = functionCallFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.Arguments$inboundSchema = z.union([z.record(z.any()), z.string()]);
/** @internal */
exports.Arguments$outboundSchema = z.union([z.record(z.any()), z.string()]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Arguments$;
(function (Arguments$) {
    /** @deprecated use `Arguments$inboundSchema` instead. */
    Arguments$.inboundSchema = exports.Arguments$inboundSchema;
    /** @deprecated use `Arguments$outboundSchema` instead. */
    Arguments$.outboundSchema = exports.Arguments$outboundSchema;
})(Arguments$ || (exports.Arguments$ = Arguments$ = {}));
function argumentsToJSON(value) {
    return JSON.stringify(exports.Arguments$outboundSchema.parse(value));
}
function argumentsFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.Arguments$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'Arguments' from JSON`);
}
/** @internal */
exports.FunctionCall$inboundSchema = z.object({
    name: z.string(),
    arguments: z.union([z.record(z.any()), z.string()]),
});
/** @internal */
exports.FunctionCall$outboundSchema = z.object({
    name: z.string(),
    arguments: z.union([z.record(z.any()), z.string()]),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FunctionCall$;
(function (FunctionCall$) {
    /** @deprecated use `FunctionCall$inboundSchema` instead. */
    FunctionCall$.inboundSchema = exports.FunctionCall$inboundSchema;
    /** @deprecated use `FunctionCall$outboundSchema` instead. */
    FunctionCall$.outboundSchema = exports.FunctionCall$outboundSchema;
})(FunctionCall$ || (exports.FunctionCall$ = FunctionCall$ = {}));
function functionCallToJSON(functionCall) {
    return JSON.stringify(exports.FunctionCall$outboundSchema.parse(functionCall));
}
function functionCallFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.FunctionCall$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'FunctionCall' from JSON`);
}
//# sourceMappingURL=functioncall.js.map