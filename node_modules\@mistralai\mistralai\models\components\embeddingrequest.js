"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmbeddingRequest$ = exports.EmbeddingRequest$outboundSchema = exports.EmbeddingRequest$inboundSchema = exports.EmbeddingRequestInputs$ = exports.EmbeddingRequestInputs$outboundSchema = exports.EmbeddingRequestInputs$inboundSchema = void 0;
exports.embeddingRequestInputsToJSON = embeddingRequestInputsToJSON;
exports.embeddingRequestInputsFromJSON = embeddingRequestInputsFromJSON;
exports.embeddingRequestToJSON = embeddingRequestToJSON;
exports.embeddingRequestFromJSON = embeddingRequestFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const embeddingdtype_js_1 = require("./embeddingdtype.js");
/** @internal */
exports.EmbeddingRequestInputs$inboundSchema = z.union([z.string(), z.array(z.string())]);
/** @internal */
exports.EmbeddingRequestInputs$outboundSchema = z.union([z.string(), z.array(z.string())]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var EmbeddingRequestInputs$;
(function (EmbeddingRequestInputs$) {
    /** @deprecated use `EmbeddingRequestInputs$inboundSchema` instead. */
    EmbeddingRequestInputs$.inboundSchema = exports.EmbeddingRequestInputs$inboundSchema;
    /** @deprecated use `EmbeddingRequestInputs$outboundSchema` instead. */
    EmbeddingRequestInputs$.outboundSchema = exports.EmbeddingRequestInputs$outboundSchema;
})(EmbeddingRequestInputs$ || (exports.EmbeddingRequestInputs$ = EmbeddingRequestInputs$ = {}));
function embeddingRequestInputsToJSON(embeddingRequestInputs) {
    return JSON.stringify(exports.EmbeddingRequestInputs$outboundSchema.parse(embeddingRequestInputs));
}
function embeddingRequestInputsFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.EmbeddingRequestInputs$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'EmbeddingRequestInputs' from JSON`);
}
/** @internal */
exports.EmbeddingRequest$inboundSchema = z.object({
    model: z.string(),
    input: z.union([z.string(), z.array(z.string())]),
    output_dimension: z.nullable(z.number().int()).optional(),
    output_dtype: embeddingdtype_js_1.EmbeddingDtype$inboundSchema.optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "input": "inputs",
        "output_dimension": "outputDimension",
        "output_dtype": "outputDtype",
    });
});
/** @internal */
exports.EmbeddingRequest$outboundSchema = z.object({
    model: z.string(),
    inputs: z.union([z.string(), z.array(z.string())]),
    outputDimension: z.nullable(z.number().int()).optional(),
    outputDtype: embeddingdtype_js_1.EmbeddingDtype$outboundSchema.optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        inputs: "input",
        outputDimension: "output_dimension",
        outputDtype: "output_dtype",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var EmbeddingRequest$;
(function (EmbeddingRequest$) {
    /** @deprecated use `EmbeddingRequest$inboundSchema` instead. */
    EmbeddingRequest$.inboundSchema = exports.EmbeddingRequest$inboundSchema;
    /** @deprecated use `EmbeddingRequest$outboundSchema` instead. */
    EmbeddingRequest$.outboundSchema = exports.EmbeddingRequest$outboundSchema;
})(EmbeddingRequest$ || (exports.EmbeddingRequest$ = EmbeddingRequest$ = {}));
function embeddingRequestToJSON(embeddingRequest) {
    return JSON.stringify(exports.EmbeddingRequest$outboundSchema.parse(embeddingRequest));
}
function embeddingRequestFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.EmbeddingRequest$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'EmbeddingRequest' from JSON`);
}
//# sourceMappingURL=embeddingrequest.js.map