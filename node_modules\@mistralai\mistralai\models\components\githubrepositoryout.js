"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GithubRepositoryOut$ = exports.GithubRepositoryOut$outboundSchema = exports.GithubRepositoryOut$inboundSchema = exports.GithubRepositoryOutType$ = exports.GithubRepositoryOutType$outboundSchema = exports.GithubRepositoryOutType$inboundSchema = exports.GithubRepositoryOutType = void 0;
exports.githubRepositoryOutToJSON = githubRepositoryOutToJSON;
exports.githubRepositoryOutFromJSON = githubRepositoryOutFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
exports.GithubRepositoryOutType = {
    Github: "github",
};
/** @internal */
exports.GithubRepositoryOutType$inboundSchema = z.nativeEnum(exports.GithubRepositoryOutType);
/** @internal */
exports.GithubRepositoryOutType$outboundSchema = exports.GithubRepositoryOutType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var GithubRepositoryOutType$;
(function (GithubRepositoryOutType$) {
    /** @deprecated use `GithubRepositoryOutType$inboundSchema` instead. */
    GithubRepositoryOutType$.inboundSchema = exports.GithubRepositoryOutType$inboundSchema;
    /** @deprecated use `GithubRepositoryOutType$outboundSchema` instead. */
    GithubRepositoryOutType$.outboundSchema = exports.GithubRepositoryOutType$outboundSchema;
})(GithubRepositoryOutType$ || (exports.GithubRepositoryOutType$ = GithubRepositoryOutType$ = {}));
/** @internal */
exports.GithubRepositoryOut$inboundSchema = z.object({
    type: exports.GithubRepositoryOutType$inboundSchema.default("github"),
    name: z.string(),
    owner: z.string(),
    ref: z.nullable(z.string()).optional(),
    weight: z.number().default(1),
    commit_id: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "commit_id": "commitId",
    });
});
/** @internal */
exports.GithubRepositoryOut$outboundSchema = z.object({
    type: exports.GithubRepositoryOutType$outboundSchema.default("github"),
    name: z.string(),
    owner: z.string(),
    ref: z.nullable(z.string()).optional(),
    weight: z.number().default(1),
    commitId: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        commitId: "commit_id",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var GithubRepositoryOut$;
(function (GithubRepositoryOut$) {
    /** @deprecated use `GithubRepositoryOut$inboundSchema` instead. */
    GithubRepositoryOut$.inboundSchema = exports.GithubRepositoryOut$inboundSchema;
    /** @deprecated use `GithubRepositoryOut$outboundSchema` instead. */
    GithubRepositoryOut$.outboundSchema = exports.GithubRepositoryOut$outboundSchema;
})(GithubRepositoryOut$ || (exports.GithubRepositoryOut$ = GithubRepositoryOut$ = {}));
function githubRepositoryOutToJSON(githubRepositoryOut) {
    return JSON.stringify(exports.GithubRepositoryOut$outboundSchema.parse(githubRepositoryOut));
}
function githubRepositoryOutFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.GithubRepositoryOut$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'GithubRepositoryOut' from JSON`);
}
//# sourceMappingURL=githubrepositoryout.js.map