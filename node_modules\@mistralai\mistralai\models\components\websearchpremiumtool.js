"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSearchPremiumTool$ = exports.WebSearchPremiumTool$outboundSchema = exports.WebSearchPremiumTool$inboundSchema = exports.WebSearchPremiumToolType$ = exports.WebSearchPremiumToolType$outboundSchema = exports.WebSearchPremiumToolType$inboundSchema = exports.WebSearchPremiumToolType = void 0;
exports.webSearchPremiumToolToJSON = webSearchPremiumToolToJSON;
exports.webSearchPremiumToolFromJSON = webSearchPremiumToolFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
exports.WebSearchPremiumToolType = {
    WebSearchPremium: "web_search_premium",
};
/** @internal */
exports.WebSearchPremiumToolType$inboundSchema = z.nativeEnum(exports.WebSearchPremiumToolType);
/** @internal */
exports.WebSearchPremiumToolType$outboundSchema = exports.WebSearchPremiumToolType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebSearchPremiumToolType$;
(function (WebSearchPremiumToolType$) {
    /** @deprecated use `WebSearchPremiumToolType$inboundSchema` instead. */
    WebSearchPremiumToolType$.inboundSchema = exports.WebSearchPremiumToolType$inboundSchema;
    /** @deprecated use `WebSearchPremiumToolType$outboundSchema` instead. */
    WebSearchPremiumToolType$.outboundSchema = exports.WebSearchPremiumToolType$outboundSchema;
})(WebSearchPremiumToolType$ || (exports.WebSearchPremiumToolType$ = WebSearchPremiumToolType$ = {}));
/** @internal */
exports.WebSearchPremiumTool$inboundSchema = z.object({
    type: exports.WebSearchPremiumToolType$inboundSchema.default("web_search_premium"),
});
/** @internal */
exports.WebSearchPremiumTool$outboundSchema = z.object({
    type: exports.WebSearchPremiumToolType$outboundSchema.default("web_search_premium"),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebSearchPremiumTool$;
(function (WebSearchPremiumTool$) {
    /** @deprecated use `WebSearchPremiumTool$inboundSchema` instead. */
    WebSearchPremiumTool$.inboundSchema = exports.WebSearchPremiumTool$inboundSchema;
    /** @deprecated use `WebSearchPremiumTool$outboundSchema` instead. */
    WebSearchPremiumTool$.outboundSchema = exports.WebSearchPremiumTool$outboundSchema;
})(WebSearchPremiumTool$ || (exports.WebSearchPremiumTool$ = WebSearchPremiumTool$ = {}));
function webSearchPremiumToolToJSON(webSearchPremiumTool) {
    return JSON.stringify(exports.WebSearchPremiumTool$outboundSchema.parse(webSearchPremiumTool));
}
function webSearchPremiumToolFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.WebSearchPremiumTool$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'WebSearchPremiumTool' from JSON`);
}
//# sourceMappingURL=websearchpremiumtool.js.map