"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnarchiveFTModelOut$ = exports.UnarchiveFTModelOut$outboundSchema = exports.UnarchiveFTModelOut$inboundSchema = exports.UnarchiveFTModelOutObject$ = exports.UnarchiveFTModelOutObject$outboundSchema = exports.UnarchiveFTModelOutObject$inboundSchema = exports.UnarchiveFTModelOutObject = void 0;
exports.unarchiveFTModelOutToJSON = unarchiveFTModelOutToJSON;
exports.unarchiveFTModelOutFromJSON = unarchiveFTModelOutFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
exports.UnarchiveFTModelOutObject = {
    Model: "model",
};
/** @internal */
exports.UnarchiveFTModelOutObject$inboundSchema = z.nativeEnum(exports.UnarchiveFTModelOutObject);
/** @internal */
exports.UnarchiveFTModelOutObject$outboundSchema = exports.UnarchiveFTModelOutObject$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var UnarchiveFTModelOutObject$;
(function (UnarchiveFTModelOutObject$) {
    /** @deprecated use `UnarchiveFTModelOutObject$inboundSchema` instead. */
    UnarchiveFTModelOutObject$.inboundSchema = exports.UnarchiveFTModelOutObject$inboundSchema;
    /** @deprecated use `UnarchiveFTModelOutObject$outboundSchema` instead. */
    UnarchiveFTModelOutObject$.outboundSchema = exports.UnarchiveFTModelOutObject$outboundSchema;
})(UnarchiveFTModelOutObject$ || (exports.UnarchiveFTModelOutObject$ = UnarchiveFTModelOutObject$ = {}));
/** @internal */
exports.UnarchiveFTModelOut$inboundSchema = z.object({
    id: z.string(),
    object: exports.UnarchiveFTModelOutObject$inboundSchema.default("model"),
    archived: z.boolean().default(false),
});
/** @internal */
exports.UnarchiveFTModelOut$outboundSchema = z.object({
    id: z.string(),
    object: exports.UnarchiveFTModelOutObject$outboundSchema.default("model"),
    archived: z.boolean().default(false),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var UnarchiveFTModelOut$;
(function (UnarchiveFTModelOut$) {
    /** @deprecated use `UnarchiveFTModelOut$inboundSchema` instead. */
    UnarchiveFTModelOut$.inboundSchema = exports.UnarchiveFTModelOut$inboundSchema;
    /** @deprecated use `UnarchiveFTModelOut$outboundSchema` instead. */
    UnarchiveFTModelOut$.outboundSchema = exports.UnarchiveFTModelOut$outboundSchema;
})(UnarchiveFTModelOut$ || (exports.UnarchiveFTModelOut$ = UnarchiveFTModelOut$ = {}));
function unarchiveFTModelOutToJSON(unarchiveFTModelOut) {
    return JSON.stringify(exports.UnarchiveFTModelOut$outboundSchema.parse(unarchiveFTModelOut));
}
function unarchiveFTModelOutFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.UnarchiveFTModelOut$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'UnarchiveFTModelOut' from JSON`);
}
//# sourceMappingURL=unarchiveftmodelout.js.map