"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FTModelCard$ = exports.FTModelCard$outboundSchema = exports.FTModelCard$inboundSchema = exports.FTModelCardType$ = exports.FTModelCardType$outboundSchema = exports.FTModelCardType$inboundSchema = exports.FTModelCardType = void 0;
exports.ftModelCardToJSON = ftModelCardToJSON;
exports.ftModelCardFromJSON = ftModelCardFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const modelcapabilities_js_1 = require("./modelcapabilities.js");
exports.FTModelCardType = {
    FineTuned: "fine-tuned",
};
/** @internal */
exports.FTModelCardType$inboundSchema = z.nativeEnum(exports.FTModelCardType);
/** @internal */
exports.FTModelCardType$outboundSchema = exports.FTModelCardType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FTModelCardType$;
(function (FTModelCardType$) {
    /** @deprecated use `FTModelCardType$inboundSchema` instead. */
    FTModelCardType$.inboundSchema = exports.FTModelCardType$inboundSchema;
    /** @deprecated use `FTModelCardType$outboundSchema` instead. */
    FTModelCardType$.outboundSchema = exports.FTModelCardType$outboundSchema;
})(FTModelCardType$ || (exports.FTModelCardType$ = FTModelCardType$ = {}));
/** @internal */
exports.FTModelCard$inboundSchema = z.object({
    id: z.string(),
    object: z.string().default("model"),
    created: z.number().int().optional(),
    owned_by: z.string().default("mistralai"),
    capabilities: modelcapabilities_js_1.ModelCapabilities$inboundSchema,
    name: z.nullable(z.string()).optional(),
    description: z.nullable(z.string()).optional(),
    max_context_length: z.number().int().default(32768),
    aliases: z.array(z.string()).optional(),
    deprecation: z.nullable(z.string().datetime({ offset: true }).transform(v => new Date(v))).optional(),
    default_model_temperature: z.nullable(z.number()).optional(),
    type: z.literal("fine-tuned").default("fine-tuned"),
    job: z.string(),
    root: z.string(),
    archived: z.boolean().default(false),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "owned_by": "ownedBy",
        "max_context_length": "maxContextLength",
        "default_model_temperature": "defaultModelTemperature",
    });
});
/** @internal */
exports.FTModelCard$outboundSchema = z.object({
    id: z.string(),
    object: z.string().default("model"),
    created: z.number().int().optional(),
    ownedBy: z.string().default("mistralai"),
    capabilities: modelcapabilities_js_1.ModelCapabilities$outboundSchema,
    name: z.nullable(z.string()).optional(),
    description: z.nullable(z.string()).optional(),
    maxContextLength: z.number().int().default(32768),
    aliases: z.array(z.string()).optional(),
    deprecation: z.nullable(z.date().transform(v => v.toISOString())).optional(),
    defaultModelTemperature: z.nullable(z.number()).optional(),
    type: z.literal("fine-tuned").default("fine-tuned"),
    job: z.string(),
    root: z.string(),
    archived: z.boolean().default(false),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        ownedBy: "owned_by",
        maxContextLength: "max_context_length",
        defaultModelTemperature: "default_model_temperature",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FTModelCard$;
(function (FTModelCard$) {
    /** @deprecated use `FTModelCard$inboundSchema` instead. */
    FTModelCard$.inboundSchema = exports.FTModelCard$inboundSchema;
    /** @deprecated use `FTModelCard$outboundSchema` instead. */
    FTModelCard$.outboundSchema = exports.FTModelCard$outboundSchema;
})(FTModelCard$ || (exports.FTModelCard$ = FTModelCard$ = {}));
function ftModelCardToJSON(ftModelCard) {
    return JSON.stringify(exports.FTModelCard$outboundSchema.parse(ftModelCard));
}
function ftModelCardFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.FTModelCard$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'FTModelCard' from JSON`);
}
//# sourceMappingURL=ftmodelcard.js.map