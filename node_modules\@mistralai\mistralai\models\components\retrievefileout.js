"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RetrieveFileOut$ = exports.RetrieveFileOut$outboundSchema = exports.RetrieveFileOut$inboundSchema = void 0;
exports.retrieveFileOutToJSON = retrieveFileOutToJSON;
exports.retrieveFileOutFromJSON = retrieveFileOutFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const filepurpose_js_1 = require("./filepurpose.js");
const sampletype_js_1 = require("./sampletype.js");
const source_js_1 = require("./source.js");
/** @internal */
exports.RetrieveFileOut$inboundSchema = z.object({
    id: z.string(),
    object: z.string(),
    bytes: z.number().int(),
    created_at: z.number().int(),
    filename: z.string(),
    purpose: filepurpose_js_1.FilePurpose$inboundSchema,
    sample_type: sampletype_js_1.SampleType$inboundSchema,
    num_lines: z.nullable(z.number().int()).optional(),
    source: source_js_1.Source$inboundSchema,
    deleted: z.boolean(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "bytes": "sizeBytes",
        "created_at": "createdAt",
        "sample_type": "sampleType",
        "num_lines": "numLines",
    });
});
/** @internal */
exports.RetrieveFileOut$outboundSchema = z.object({
    id: z.string(),
    object: z.string(),
    sizeBytes: z.number().int(),
    createdAt: z.number().int(),
    filename: z.string(),
    purpose: filepurpose_js_1.FilePurpose$outboundSchema,
    sampleType: sampletype_js_1.SampleType$outboundSchema,
    numLines: z.nullable(z.number().int()).optional(),
    source: source_js_1.Source$outboundSchema,
    deleted: z.boolean(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        sizeBytes: "bytes",
        createdAt: "created_at",
        sampleType: "sample_type",
        numLines: "num_lines",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var RetrieveFileOut$;
(function (RetrieveFileOut$) {
    /** @deprecated use `RetrieveFileOut$inboundSchema` instead. */
    RetrieveFileOut$.inboundSchema = exports.RetrieveFileOut$inboundSchema;
    /** @deprecated use `RetrieveFileOut$outboundSchema` instead. */
    RetrieveFileOut$.outboundSchema = exports.RetrieveFileOut$outboundSchema;
})(RetrieveFileOut$ || (exports.RetrieveFileOut$ = RetrieveFileOut$ = {}));
function retrieveFileOutToJSON(retrieveFileOut) {
    return JSON.stringify(exports.RetrieveFileOut$outboundSchema.parse(retrieveFileOut));
}
function retrieveFileOutFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.RetrieveFileOut$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'RetrieveFileOut' from JSON`);
}
//# sourceMappingURL=retrievefileout.js.map