"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentConversation$ = exports.AgentConversation$outboundSchema = exports.AgentConversation$inboundSchema = exports.AgentConversationObject$ = exports.AgentConversationObject$outboundSchema = exports.AgentConversationObject$inboundSchema = exports.AgentConversationObject = void 0;
exports.agentConversationToJSON = agentConversationToJSON;
exports.agentConversationFromJSON = agentConversationFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
exports.AgentConversationObject = {
    Conversation: "conversation",
};
/** @internal */
exports.AgentConversationObject$inboundSchema = z.nativeEnum(exports.AgentConversationObject);
/** @internal */
exports.AgentConversationObject$outboundSchema = exports.AgentConversationObject$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentConversationObject$;
(function (AgentConversationObject$) {
    /** @deprecated use `AgentConversationObject$inboundSchema` instead. */
    AgentConversationObject$.inboundSchema = exports.AgentConversationObject$inboundSchema;
    /** @deprecated use `AgentConversationObject$outboundSchema` instead. */
    AgentConversationObject$.outboundSchema = exports.AgentConversationObject$outboundSchema;
})(AgentConversationObject$ || (exports.AgentConversationObject$ = AgentConversationObject$ = {}));
/** @internal */
exports.AgentConversation$inboundSchema = z.object({
    name: z.nullable(z.string()).optional(),
    description: z.nullable(z.string()).optional(),
    object: exports.AgentConversationObject$inboundSchema.default("conversation"),
    id: z.string(),
    created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
    updated_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
    agent_id: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "created_at": "createdAt",
        "updated_at": "updatedAt",
        "agent_id": "agentId",
    });
});
/** @internal */
exports.AgentConversation$outboundSchema = z.object({
    name: z.nullable(z.string()).optional(),
    description: z.nullable(z.string()).optional(),
    object: exports.AgentConversationObject$outboundSchema.default("conversation"),
    id: z.string(),
    createdAt: z.date().transform(v => v.toISOString()),
    updatedAt: z.date().transform(v => v.toISOString()),
    agentId: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        createdAt: "created_at",
        updatedAt: "updated_at",
        agentId: "agent_id",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentConversation$;
(function (AgentConversation$) {
    /** @deprecated use `AgentConversation$inboundSchema` instead. */
    AgentConversation$.inboundSchema = exports.AgentConversation$inboundSchema;
    /** @deprecated use `AgentConversation$outboundSchema` instead. */
    AgentConversation$.outboundSchema = exports.AgentConversation$outboundSchema;
})(AgentConversation$ || (exports.AgentConversation$ = AgentConversation$ = {}));
function agentConversationToJSON(agentConversation) {
    return JSON.stringify(exports.AgentConversation$outboundSchema.parse(agentConversation));
}
function agentConversationFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.AgentConversation$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'AgentConversation' from JSON`);
}
//# sourceMappingURL=agentconversation.js.map