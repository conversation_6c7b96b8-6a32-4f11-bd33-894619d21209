"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatCompletionResponse$ = exports.ChatCompletionResponse$outboundSchema = exports.ChatCompletionResponse$inboundSchema = void 0;
exports.chatCompletionResponseToJSON = chatCompletionResponseToJSON;
exports.chatCompletionResponseFromJSON = chatCompletionResponseFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const chatcompletionchoice_js_1 = require("./chatcompletionchoice.js");
const usageinfo_js_1 = require("./usageinfo.js");
/** @internal */
exports.ChatCompletionResponse$inboundSchema = z.object({
    id: z.string(),
    object: z.string(),
    model: z.string(),
    usage: usageinfo_js_1.UsageInfo$inboundSchema,
    created: z.number().int(),
    choices: z.array(chatcompletionchoice_js_1.ChatCompletionChoice$inboundSchema),
});
/** @internal */
exports.ChatCompletionResponse$outboundSchema = z.object({
    id: z.string(),
    object: z.string(),
    model: z.string(),
    usage: usageinfo_js_1.UsageInfo$outboundSchema,
    created: z.number().int(),
    choices: z.array(chatcompletionchoice_js_1.ChatCompletionChoice$outboundSchema),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ChatCompletionResponse$;
(function (ChatCompletionResponse$) {
    /** @deprecated use `ChatCompletionResponse$inboundSchema` instead. */
    ChatCompletionResponse$.inboundSchema = exports.ChatCompletionResponse$inboundSchema;
    /** @deprecated use `ChatCompletionResponse$outboundSchema` instead. */
    ChatCompletionResponse$.outboundSchema = exports.ChatCompletionResponse$outboundSchema;
})(ChatCompletionResponse$ || (exports.ChatCompletionResponse$ = ChatCompletionResponse$ = {}));
function chatCompletionResponseToJSON(chatCompletionResponse) {
    return JSON.stringify(exports.ChatCompletionResponse$outboundSchema.parse(chatCompletionResponse));
}
function chatCompletionResponseFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ChatCompletionResponse$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ChatCompletionResponse' from JSON`);
}
//# sourceMappingURL=chatcompletionresponse.js.map