"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SDKHooks = void 0;
const registration_js_1 = require("./registration.js");
class SDKHooks {
    constructor() {
        this.sdkInitHooks = [];
        this.beforeCreateRequestHooks = [];
        this.beforeRequestHooks = [];
        this.afterSuccessHooks = [];
        this.afterErrorHooks = [];
        const presetHooks = [];
        for (const hook of presetHooks) {
            if ("sdkInit" in hook) {
                this.registerSDKInitHook(hook);
            }
            if ("beforeCreateRequest" in hook) {
                this.registerBeforeCreateRequestHook(hook);
            }
            if ("beforeRequest" in hook) {
                this.registerBeforeRequestHook(hook);
            }
            if ("afterSuccess" in hook) {
                this.registerAfterSuccessHook(hook);
            }
            if ("afterError" in hook) {
                this.registerAfterErrorHook(hook);
            }
        }
        (0, registration_js_1.initHooks)(this);
    }
    registerSDKInitHook(hook) {
        this.sdkInitHooks.push(hook);
    }
    registerBeforeCreateRequestHook(hook) {
        this.beforeCreateRequestHooks.push(hook);
    }
    registerBeforeRequestHook(hook) {
        this.beforeRequestHooks.push(hook);
    }
    registerAfterSuccessHook(hook) {
        this.afterSuccessHooks.push(hook);
    }
    registerAfterErrorHook(hook) {
        this.afterErrorHooks.push(hook);
    }
    sdkInit(opts) {
        return this.sdkInitHooks.reduce((opts, hook) => hook.sdkInit(opts), opts);
    }
    beforeCreateRequest(hookCtx, input) {
        let inp = input;
        for (const hook of this.beforeCreateRequestHooks) {
            inp = hook.beforeCreateRequest(hookCtx, inp);
        }
        return inp;
    }
    async beforeRequest(hookCtx, request) {
        let req = request;
        for (const hook of this.beforeRequestHooks) {
            req = await hook.beforeRequest(hookCtx, req);
        }
        return req;
    }
    async afterSuccess(hookCtx, response) {
        let res = response;
        for (const hook of this.afterSuccessHooks) {
            res = await hook.afterSuccess(hookCtx, res);
        }
        return res;
    }
    async afterError(hookCtx, response, error) {
        let res = response;
        let err = error;
        for (const hook of this.afterErrorHooks) {
            const result = await hook.afterError(hookCtx, res, err);
            res = result.response;
            err = result.error;
        }
        return { response: res, error: err };
    }
}
exports.SDKHooks = SDKHooks;
//# sourceMappingURL=hooks.js.map