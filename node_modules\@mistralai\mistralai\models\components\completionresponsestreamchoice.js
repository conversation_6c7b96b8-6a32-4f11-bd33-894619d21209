"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompletionResponseStreamChoice$ = exports.CompletionResponseStreamChoice$outboundSchema = exports.CompletionResponseStreamChoice$inboundSchema = exports.CompletionResponseStreamChoiceFinishReason$ = exports.CompletionResponseStreamChoiceFinishReason$outboundSchema = exports.CompletionResponseStreamChoiceFinishReason$inboundSchema = exports.CompletionResponseStreamChoiceFinishReason = void 0;
exports.completionResponseStreamChoiceToJSON = completionResponseStreamChoiceToJSON;
exports.completionResponseStreamChoiceFromJSON = completionResponseStreamChoiceFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const enums_js_1 = require("../../types/enums.js");
const deltamessage_js_1 = require("./deltamessage.js");
exports.CompletionResponseStreamChoiceFinishReason = {
    Stop: "stop",
    Length: "length",
    Error: "error",
    ToolCalls: "tool_calls",
};
/** @internal */
exports.CompletionResponseStreamChoiceFinishReason$inboundSchema = z
    .union([
    z.nativeEnum(exports.CompletionResponseStreamChoiceFinishReason),
    z.string().transform(enums_js_1.catchUnrecognizedEnum),
]);
/** @internal */
exports.CompletionResponseStreamChoiceFinishReason$outboundSchema = z.union([
    z.nativeEnum(exports.CompletionResponseStreamChoiceFinishReason),
    z.string().and(z.custom()),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var CompletionResponseStreamChoiceFinishReason$;
(function (CompletionResponseStreamChoiceFinishReason$) {
    /** @deprecated use `CompletionResponseStreamChoiceFinishReason$inboundSchema` instead. */
    CompletionResponseStreamChoiceFinishReason$.inboundSchema = exports.CompletionResponseStreamChoiceFinishReason$inboundSchema;
    /** @deprecated use `CompletionResponseStreamChoiceFinishReason$outboundSchema` instead. */
    CompletionResponseStreamChoiceFinishReason$.outboundSchema = exports.CompletionResponseStreamChoiceFinishReason$outboundSchema;
})(CompletionResponseStreamChoiceFinishReason$ || (exports.CompletionResponseStreamChoiceFinishReason$ = CompletionResponseStreamChoiceFinishReason$ = {}));
/** @internal */
exports.CompletionResponseStreamChoice$inboundSchema = z.object({
    index: z.number().int(),
    delta: deltamessage_js_1.DeltaMessage$inboundSchema,
    finish_reason: z.nullable(exports.CompletionResponseStreamChoiceFinishReason$inboundSchema),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "finish_reason": "finishReason",
    });
});
/** @internal */
exports.CompletionResponseStreamChoice$outboundSchema = z.object({
    index: z.number().int(),
    delta: deltamessage_js_1.DeltaMessage$outboundSchema,
    finishReason: z.nullable(exports.CompletionResponseStreamChoiceFinishReason$outboundSchema),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        finishReason: "finish_reason",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var CompletionResponseStreamChoice$;
(function (CompletionResponseStreamChoice$) {
    /** @deprecated use `CompletionResponseStreamChoice$inboundSchema` instead. */
    CompletionResponseStreamChoice$.inboundSchema = exports.CompletionResponseStreamChoice$inboundSchema;
    /** @deprecated use `CompletionResponseStreamChoice$outboundSchema` instead. */
    CompletionResponseStreamChoice$.outboundSchema = exports.CompletionResponseStreamChoice$outboundSchema;
})(CompletionResponseStreamChoice$ || (exports.CompletionResponseStreamChoice$ = CompletionResponseStreamChoice$ = {}));
function completionResponseStreamChoiceToJSON(completionResponseStreamChoice) {
    return JSON.stringify(exports.CompletionResponseStreamChoice$outboundSchema.parse(completionResponseStreamChoice));
}
function completionResponseStreamChoiceFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.CompletionResponseStreamChoice$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'CompletionResponseStreamChoice' from JSON`);
}
//# sourceMappingURL=completionresponsestreamchoice.js.map