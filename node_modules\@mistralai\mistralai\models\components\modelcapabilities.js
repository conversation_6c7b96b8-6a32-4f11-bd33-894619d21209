"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelCapabilities$ = exports.ModelCapabilities$outboundSchema = exports.ModelCapabilities$inboundSchema = void 0;
exports.modelCapabilitiesToJSON = modelCapabilitiesToJSON;
exports.modelCapabilitiesFromJSON = modelCapabilitiesFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.ModelCapabilities$inboundSchema = z.object({
    completion_chat: z.boolean().default(true),
    completion_fim: z.boolean().default(false),
    function_calling: z.boolean().default(true),
    fine_tuning: z.boolean().default(false),
    vision: z.boolean().default(false),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "completion_chat": "completionChat",
        "completion_fim": "completionFim",
        "function_calling": "functionCalling",
        "fine_tuning": "fineTuning",
    });
});
/** @internal */
exports.ModelCapabilities$outboundSchema = z.object({
    completionChat: z.boolean().default(true),
    completionFim: z.boolean().default(false),
    functionCalling: z.boolean().default(true),
    fineTuning: z.boolean().default(false),
    vision: z.boolean().default(false),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        completionChat: "completion_chat",
        completionFim: "completion_fim",
        functionCalling: "function_calling",
        fineTuning: "fine_tuning",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ModelCapabilities$;
(function (ModelCapabilities$) {
    /** @deprecated use `ModelCapabilities$inboundSchema` instead. */
    ModelCapabilities$.inboundSchema = exports.ModelCapabilities$inboundSchema;
    /** @deprecated use `ModelCapabilities$outboundSchema` instead. */
    ModelCapabilities$.outboundSchema = exports.ModelCapabilities$outboundSchema;
})(ModelCapabilities$ || (exports.ModelCapabilities$ = ModelCapabilities$ = {}));
function modelCapabilitiesToJSON(modelCapabilities) {
    return JSON.stringify(exports.ModelCapabilities$outboundSchema.parse(modelCapabilities));
}
function modelCapabilitiesFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ModelCapabilities$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ModelCapabilities' from JSON`);
}
//# sourceMappingURL=modelcapabilities.js.map