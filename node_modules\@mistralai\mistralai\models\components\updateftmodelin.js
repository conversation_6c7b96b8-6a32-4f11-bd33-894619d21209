"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateFTModelIn$ = exports.UpdateFTModelIn$outboundSchema = exports.UpdateFTModelIn$inboundSchema = void 0;
exports.updateFTModelInToJSON = updateFTModelInToJSON;
exports.updateFTModelInFromJSON = updateFTModelInFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.UpdateFTModelIn$inboundSchema = z.object({
    name: z.nullable(z.string()).optional(),
    description: z.nullable(z.string()).optional(),
});
/** @internal */
exports.UpdateFTModelIn$outboundSchema = z.object({
    name: z.nullable(z.string()).optional(),
    description: z.nullable(z.string()).optional(),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var UpdateFTModelIn$;
(function (UpdateFTModelIn$) {
    /** @deprecated use `UpdateFTModelIn$inboundSchema` instead. */
    UpdateFTModelIn$.inboundSchema = exports.UpdateFTModelIn$inboundSchema;
    /** @deprecated use `UpdateFTModelIn$outboundSchema` instead. */
    UpdateFTModelIn$.outboundSchema = exports.UpdateFTModelIn$outboundSchema;
})(UpdateFTModelIn$ || (exports.UpdateFTModelIn$ = UpdateFTModelIn$ = {}));
function updateFTModelInToJSON(updateFTModelIn) {
    return JSON.stringify(exports.UpdateFTModelIn$outboundSchema.parse(updateFTModelIn));
}
function updateFTModelInFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.UpdateFTModelIn$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'UpdateFTModelIn' from JSON`);
}
//# sourceMappingURL=updateftmodelin.js.map