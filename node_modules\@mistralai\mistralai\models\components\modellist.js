"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelList$ = exports.ModelList$outboundSchema = exports.ModelList$inboundSchema = exports.Data$ = exports.Data$outboundSchema = exports.Data$inboundSchema = void 0;
exports.dataToJSON = dataToJSON;
exports.dataFromJSON = dataFromJSON;
exports.modelListToJSON = modelListToJSON;
exports.modelListFromJSON = modelListFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const basemodelcard_js_1 = require("./basemodelcard.js");
const ftmodelcard_js_1 = require("./ftmodelcard.js");
/** @internal */
exports.Data$inboundSchema = z
    .union([
    basemodelcard_js_1.BaseModelCard$inboundSchema.and(z.object({ type: z.literal("base") }).transform((v) => ({
        type: v.type,
    }))),
    ftmodelcard_js_1.FTModelCard$inboundSchema.and(z.object({ type: z.literal("fine-tuned") }).transform((v) => ({
        type: v.type,
    }))),
]);
/** @internal */
exports.Data$outboundSchema = z.union([
    basemodelcard_js_1.BaseModelCard$outboundSchema.and(z.object({ type: z.literal("base") }).transform((v) => ({
        type: v.type,
    }))),
    ftmodelcard_js_1.FTModelCard$outboundSchema.and(z.object({ type: z.literal("fine-tuned") }).transform((v) => ({
        type: v.type,
    }))),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Data$;
(function (Data$) {
    /** @deprecated use `Data$inboundSchema` instead. */
    Data$.inboundSchema = exports.Data$inboundSchema;
    /** @deprecated use `Data$outboundSchema` instead. */
    Data$.outboundSchema = exports.Data$outboundSchema;
})(Data$ || (exports.Data$ = Data$ = {}));
function dataToJSON(data) {
    return JSON.stringify(exports.Data$outboundSchema.parse(data));
}
function dataFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.Data$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'Data' from JSON`);
}
/** @internal */
exports.ModelList$inboundSchema = z.object({
    object: z.string().default("list"),
    data: z.array(z.union([
        basemodelcard_js_1.BaseModelCard$inboundSchema.and(z.object({ type: z.literal("base") }).transform((v) => ({
            type: v.type,
        }))),
        ftmodelcard_js_1.FTModelCard$inboundSchema.and(z.object({ type: z.literal("fine-tuned") }).transform((v) => ({
            type: v.type,
        }))),
    ])).optional(),
});
/** @internal */
exports.ModelList$outboundSchema = z.object({
    object: z.string().default("list"),
    data: z.array(z.union([
        basemodelcard_js_1.BaseModelCard$outboundSchema.and(z.object({ type: z.literal("base") }).transform((v) => ({
            type: v.type,
        }))),
        ftmodelcard_js_1.FTModelCard$outboundSchema.and(z.object({ type: z.literal("fine-tuned") }).transform((v) => ({
            type: v.type,
        }))),
    ])).optional(),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ModelList$;
(function (ModelList$) {
    /** @deprecated use `ModelList$inboundSchema` instead. */
    ModelList$.inboundSchema = exports.ModelList$inboundSchema;
    /** @deprecated use `ModelList$outboundSchema` instead. */
    ModelList$.outboundSchema = exports.ModelList$outboundSchema;
})(ModelList$ || (exports.ModelList$ = ModelList$ = {}));
function modelListToJSON(modelList) {
    return JSON.stringify(exports.ModelList$outboundSchema.parse(modelList));
}
function modelListFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ModelList$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ModelList' from JSON`);
}
//# sourceMappingURL=modellist.js.map