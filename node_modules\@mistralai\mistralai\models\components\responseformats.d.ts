import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";
/**
 * An object specifying the format that the model must output. Setting to `{ "type": "json_object" }` enables JSON mode, which guarantees the message the model generates is in JSON. When using JSON mode you MUST also instruct the model to produce JSON yourself with a system or a user message.
 */
export declare const ResponseFormats: {
    readonly Text: "text";
    readonly JsonObject: "json_object";
    readonly JsonSchema: "json_schema";
};
/**
 * An object specifying the format that the model must output. Setting to `{ "type": "json_object" }` enables JSON mode, which guarantees the message the model generates is in JSON. When using JSON mode you MUST also instruct the model to produce JSON yourself with a system or a user message.
 */
export type ResponseFormats = ClosedEnum<typeof ResponseFormats>;
/** @internal */
export declare const ResponseFormats$inboundSchema: z.ZodNativeEnum<typeof ResponseFormats>;
/** @internal */
export declare const ResponseFormats$outboundSchema: z.ZodNativeEnum<typeof ResponseFormats>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace ResponseFormats$ {
    /** @deprecated use `ResponseFormats$inboundSchema` instead. */
    const inboundSchema: z.ZodNativeEnum<{
        readonly Text: "text";
        readonly JsonObject: "json_object";
        readonly JsonSchema: "json_schema";
    }>;
    /** @deprecated use `ResponseFormats$outboundSchema` instead. */
    const outboundSchema: z.ZodNativeEnum<{
        readonly Text: "text";
        readonly JsonObject: "json_object";
        readonly JsonSchema: "json_schema";
    }>;
}
//# sourceMappingURL=responseformats.d.ts.map