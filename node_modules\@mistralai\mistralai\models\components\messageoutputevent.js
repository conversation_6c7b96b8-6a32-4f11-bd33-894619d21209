"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageOutputEvent$ = exports.MessageOutputEvent$outboundSchema = exports.MessageOutputEvent$inboundSchema = exports.MessageOutputEventContent$ = exports.MessageOutputEventContent$outboundSchema = exports.MessageOutputEventContent$inboundSchema = exports.MessageOutputEventRole$ = exports.MessageOutputEventRole$outboundSchema = exports.MessageOutputEventRole$inboundSchema = exports.MessageOutputEventType$ = exports.MessageOutputEventType$outboundSchema = exports.MessageOutputEventType$inboundSchema = exports.MessageOutputEventRole = exports.MessageOutputEventType = void 0;
exports.messageOutputEventContentToJSON = messageOutputEventContentToJSON;
exports.messageOutputEventContentFromJSON = messageOutputEventContentFromJSON;
exports.messageOutputEventToJSON = messageOutputEventToJSON;
exports.messageOutputEventFromJSON = messageOutputEventFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const outputcontentchunks_js_1 = require("./outputcontentchunks.js");
exports.MessageOutputEventType = {
    MessageOutputDelta: "message.output.delta",
};
exports.MessageOutputEventRole = {
    Assistant: "assistant",
};
/** @internal */
exports.MessageOutputEventType$inboundSchema = z.nativeEnum(exports.MessageOutputEventType);
/** @internal */
exports.MessageOutputEventType$outboundSchema = exports.MessageOutputEventType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MessageOutputEventType$;
(function (MessageOutputEventType$) {
    /** @deprecated use `MessageOutputEventType$inboundSchema` instead. */
    MessageOutputEventType$.inboundSchema = exports.MessageOutputEventType$inboundSchema;
    /** @deprecated use `MessageOutputEventType$outboundSchema` instead. */
    MessageOutputEventType$.outboundSchema = exports.MessageOutputEventType$outboundSchema;
})(MessageOutputEventType$ || (exports.MessageOutputEventType$ = MessageOutputEventType$ = {}));
/** @internal */
exports.MessageOutputEventRole$inboundSchema = z.nativeEnum(exports.MessageOutputEventRole);
/** @internal */
exports.MessageOutputEventRole$outboundSchema = exports.MessageOutputEventRole$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MessageOutputEventRole$;
(function (MessageOutputEventRole$) {
    /** @deprecated use `MessageOutputEventRole$inboundSchema` instead. */
    MessageOutputEventRole$.inboundSchema = exports.MessageOutputEventRole$inboundSchema;
    /** @deprecated use `MessageOutputEventRole$outboundSchema` instead. */
    MessageOutputEventRole$.outboundSchema = exports.MessageOutputEventRole$outboundSchema;
})(MessageOutputEventRole$ || (exports.MessageOutputEventRole$ = MessageOutputEventRole$ = {}));
/** @internal */
exports.MessageOutputEventContent$inboundSchema = z.union([z.string(), outputcontentchunks_js_1.OutputContentChunks$inboundSchema]);
/** @internal */
exports.MessageOutputEventContent$outboundSchema = z.union([z.string(), outputcontentchunks_js_1.OutputContentChunks$outboundSchema]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MessageOutputEventContent$;
(function (MessageOutputEventContent$) {
    /** @deprecated use `MessageOutputEventContent$inboundSchema` instead. */
    MessageOutputEventContent$.inboundSchema = exports.MessageOutputEventContent$inboundSchema;
    /** @deprecated use `MessageOutputEventContent$outboundSchema` instead. */
    MessageOutputEventContent$.outboundSchema = exports.MessageOutputEventContent$outboundSchema;
})(MessageOutputEventContent$ || (exports.MessageOutputEventContent$ = MessageOutputEventContent$ = {}));
function messageOutputEventContentToJSON(messageOutputEventContent) {
    return JSON.stringify(exports.MessageOutputEventContent$outboundSchema.parse(messageOutputEventContent));
}
function messageOutputEventContentFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.MessageOutputEventContent$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'MessageOutputEventContent' from JSON`);
}
/** @internal */
exports.MessageOutputEvent$inboundSchema = z.object({
    type: exports.MessageOutputEventType$inboundSchema.default("message.output.delta"),
    created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
        .optional(),
    output_index: z.number().int().default(0),
    id: z.string(),
    content_index: z.number().int().default(0),
    model: z.nullable(z.string()).optional(),
    agent_id: z.nullable(z.string()).optional(),
    role: exports.MessageOutputEventRole$inboundSchema.default("assistant"),
    content: z.union([z.string(), outputcontentchunks_js_1.OutputContentChunks$inboundSchema]),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "created_at": "createdAt",
        "output_index": "outputIndex",
        "content_index": "contentIndex",
        "agent_id": "agentId",
    });
});
/** @internal */
exports.MessageOutputEvent$outboundSchema = z.object({
    type: exports.MessageOutputEventType$outboundSchema.default("message.output.delta"),
    createdAt: z.date().transform(v => v.toISOString()).optional(),
    outputIndex: z.number().int().default(0),
    id: z.string(),
    contentIndex: z.number().int().default(0),
    model: z.nullable(z.string()).optional(),
    agentId: z.nullable(z.string()).optional(),
    role: exports.MessageOutputEventRole$outboundSchema.default("assistant"),
    content: z.union([z.string(), outputcontentchunks_js_1.OutputContentChunks$outboundSchema]),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        createdAt: "created_at",
        outputIndex: "output_index",
        contentIndex: "content_index",
        agentId: "agent_id",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MessageOutputEvent$;
(function (MessageOutputEvent$) {
    /** @deprecated use `MessageOutputEvent$inboundSchema` instead. */
    MessageOutputEvent$.inboundSchema = exports.MessageOutputEvent$inboundSchema;
    /** @deprecated use `MessageOutputEvent$outboundSchema` instead. */
    MessageOutputEvent$.outboundSchema = exports.MessageOutputEvent$outboundSchema;
})(MessageOutputEvent$ || (exports.MessageOutputEvent$ = MessageOutputEvent$ = {}));
function messageOutputEventToJSON(messageOutputEvent) {
    return JSON.stringify(exports.MessageOutputEvent$outboundSchema.parse(messageOutputEvent));
}
function messageOutputEventFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.MessageOutputEvent$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'MessageOutputEvent' from JSON`);
}
//# sourceMappingURL=messageoutputevent.js.map