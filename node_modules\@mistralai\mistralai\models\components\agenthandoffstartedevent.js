"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentHandoffStartedEvent$ = exports.AgentHandoffStartedEvent$outboundSchema = exports.AgentHandoffStartedEvent$inboundSchema = exports.AgentHandoffStartedEventType$ = exports.AgentHandoffStartedEventType$outboundSchema = exports.AgentHandoffStartedEventType$inboundSchema = exports.AgentHandoffStartedEventType = void 0;
exports.agentHandoffStartedEventToJSON = agentHandoffStartedEventToJSON;
exports.agentHandoffStartedEventFromJSON = agentHandoffStartedEventFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
exports.AgentHandoffStartedEventType = {
    AgentHandoffStarted: "agent.handoff.started",
};
/** @internal */
exports.AgentHandoffStartedEventType$inboundSchema = z.nativeEnum(exports.AgentHandoffStartedEventType);
/** @internal */
exports.AgentHandoffStartedEventType$outboundSchema = exports.AgentHandoffStartedEventType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentHandoffStartedEventType$;
(function (AgentHandoffStartedEventType$) {
    /** @deprecated use `AgentHandoffStartedEventType$inboundSchema` instead. */
    AgentHandoffStartedEventType$.inboundSchema = exports.AgentHandoffStartedEventType$inboundSchema;
    /** @deprecated use `AgentHandoffStartedEventType$outboundSchema` instead. */
    AgentHandoffStartedEventType$.outboundSchema = exports.AgentHandoffStartedEventType$outboundSchema;
})(AgentHandoffStartedEventType$ || (exports.AgentHandoffStartedEventType$ = AgentHandoffStartedEventType$ = {}));
/** @internal */
exports.AgentHandoffStartedEvent$inboundSchema = z.object({
    type: exports.AgentHandoffStartedEventType$inboundSchema.default("agent.handoff.started"),
    created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
        .optional(),
    output_index: z.number().int().default(0),
    id: z.string(),
    previous_agent_id: z.string(),
    previous_agent_name: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "created_at": "createdAt",
        "output_index": "outputIndex",
        "previous_agent_id": "previousAgentId",
        "previous_agent_name": "previousAgentName",
    });
});
/** @internal */
exports.AgentHandoffStartedEvent$outboundSchema = z.object({
    type: exports.AgentHandoffStartedEventType$outboundSchema.default("agent.handoff.started"),
    createdAt: z.date().transform(v => v.toISOString()).optional(),
    outputIndex: z.number().int().default(0),
    id: z.string(),
    previousAgentId: z.string(),
    previousAgentName: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        createdAt: "created_at",
        outputIndex: "output_index",
        previousAgentId: "previous_agent_id",
        previousAgentName: "previous_agent_name",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentHandoffStartedEvent$;
(function (AgentHandoffStartedEvent$) {
    /** @deprecated use `AgentHandoffStartedEvent$inboundSchema` instead. */
    AgentHandoffStartedEvent$.inboundSchema = exports.AgentHandoffStartedEvent$inboundSchema;
    /** @deprecated use `AgentHandoffStartedEvent$outboundSchema` instead. */
    AgentHandoffStartedEvent$.outboundSchema = exports.AgentHandoffStartedEvent$outboundSchema;
})(AgentHandoffStartedEvent$ || (exports.AgentHandoffStartedEvent$ = AgentHandoffStartedEvent$ = {}));
function agentHandoffStartedEventToJSON(agentHandoffStartedEvent) {
    return JSON.stringify(exports.AgentHandoffStartedEvent$outboundSchema.parse(agentHandoffStartedEvent));
}
function agentHandoffStartedEventFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.AgentHandoffStartedEvent$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'AgentHandoffStartedEvent' from JSON`);
}
//# sourceMappingURL=agenthandoffstartedevent.js.map