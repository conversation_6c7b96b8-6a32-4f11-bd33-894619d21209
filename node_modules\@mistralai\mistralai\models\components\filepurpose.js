"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FilePurpose$ = exports.FilePurpose$outboundSchema = exports.FilePurpose$inboundSchema = exports.FilePurpose = void 0;
const z = __importStar(require("zod"));
const enums_js_1 = require("../../types/enums.js");
exports.FilePurpose = {
    FineTune: "fine-tune",
    Batch: "batch",
    Ocr: "ocr",
};
/** @internal */
exports.FilePurpose$inboundSchema = z
    .union([
    z.nativeEnum(exports.FilePurpose),
    z.string().transform(enums_js_1.catchUnrecognizedEnum),
]);
/** @internal */
exports.FilePurpose$outboundSchema = z.union([
    z.nativeEnum(exports.FilePurpose),
    z.string().and(z.custom()),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FilePurpose$;
(function (FilePurpose$) {
    /** @deprecated use `FilePurpose$inboundSchema` instead. */
    FilePurpose$.inboundSchema = exports.FilePurpose$inboundSchema;
    /** @deprecated use `FilePurpose$outboundSchema` instead. */
    FilePurpose$.outboundSchema = exports.FilePurpose$outboundSchema;
})(FilePurpose$ || (exports.FilePurpose$ = FilePurpose$ = {}));
//# sourceMappingURL=filepurpose.js.map