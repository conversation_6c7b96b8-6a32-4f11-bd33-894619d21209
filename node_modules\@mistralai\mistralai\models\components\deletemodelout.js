"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteModelOut$ = exports.DeleteModelOut$outboundSchema = exports.DeleteModelOut$inboundSchema = void 0;
exports.deleteModelOutToJSON = deleteModelOutToJSON;
exports.deleteModelOutFromJSON = deleteModelOutFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.DeleteModelOut$inboundSchema = z.object({
    id: z.string(),
    object: z.string().default("model"),
    deleted: z.boolean().default(true),
});
/** @internal */
exports.DeleteModelOut$outboundSchema = z.object({
    id: z.string(),
    object: z.string().default("model"),
    deleted: z.boolean().default(true),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var DeleteModelOut$;
(function (DeleteModelOut$) {
    /** @deprecated use `DeleteModelOut$inboundSchema` instead. */
    DeleteModelOut$.inboundSchema = exports.DeleteModelOut$inboundSchema;
    /** @deprecated use `DeleteModelOut$outboundSchema` instead. */
    DeleteModelOut$.outboundSchema = exports.DeleteModelOut$outboundSchema;
})(DeleteModelOut$ || (exports.DeleteModelOut$ = DeleteModelOut$ = {}));
function deleteModelOutToJSON(deleteModelOut) {
    return JSON.stringify(exports.DeleteModelOut$outboundSchema.parse(deleteModelOut));
}
function deleteModelOutFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.DeleteModelOut$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'DeleteModelOut' from JSON`);
}
//# sourceMappingURL=deletemodelout.js.map