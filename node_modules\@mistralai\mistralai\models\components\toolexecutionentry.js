"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolExecutionEntry$ = exports.ToolExecutionEntry$outboundSchema = exports.ToolExecutionEntry$inboundSchema = exports.ToolExecutionEntryType$ = exports.ToolExecutionEntryType$outboundSchema = exports.ToolExecutionEntryType$inboundSchema = exports.ToolExecutionEntryObject$ = exports.ToolExecutionEntryObject$outboundSchema = exports.ToolExecutionEntryObject$inboundSchema = exports.ToolExecutionEntryType = exports.ToolExecutionEntryObject = void 0;
exports.toolExecutionEntryToJSON = toolExecutionEntryToJSON;
exports.toolExecutionEntryFromJSON = toolExecutionEntryFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const builtinconnectors_js_1 = require("./builtinconnectors.js");
exports.ToolExecutionEntryObject = {
    Entry: "entry",
};
exports.ToolExecutionEntryType = {
    ToolExecution: "tool.execution",
};
/** @internal */
exports.ToolExecutionEntryObject$inboundSchema = z.nativeEnum(exports.ToolExecutionEntryObject);
/** @internal */
exports.ToolExecutionEntryObject$outboundSchema = exports.ToolExecutionEntryObject$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ToolExecutionEntryObject$;
(function (ToolExecutionEntryObject$) {
    /** @deprecated use `ToolExecutionEntryObject$inboundSchema` instead. */
    ToolExecutionEntryObject$.inboundSchema = exports.ToolExecutionEntryObject$inboundSchema;
    /** @deprecated use `ToolExecutionEntryObject$outboundSchema` instead. */
    ToolExecutionEntryObject$.outboundSchema = exports.ToolExecutionEntryObject$outboundSchema;
})(ToolExecutionEntryObject$ || (exports.ToolExecutionEntryObject$ = ToolExecutionEntryObject$ = {}));
/** @internal */
exports.ToolExecutionEntryType$inboundSchema = z.nativeEnum(exports.ToolExecutionEntryType);
/** @internal */
exports.ToolExecutionEntryType$outboundSchema = exports.ToolExecutionEntryType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ToolExecutionEntryType$;
(function (ToolExecutionEntryType$) {
    /** @deprecated use `ToolExecutionEntryType$inboundSchema` instead. */
    ToolExecutionEntryType$.inboundSchema = exports.ToolExecutionEntryType$inboundSchema;
    /** @deprecated use `ToolExecutionEntryType$outboundSchema` instead. */
    ToolExecutionEntryType$.outboundSchema = exports.ToolExecutionEntryType$outboundSchema;
})(ToolExecutionEntryType$ || (exports.ToolExecutionEntryType$ = ToolExecutionEntryType$ = {}));
/** @internal */
exports.ToolExecutionEntry$inboundSchema = z.object({
    object: exports.ToolExecutionEntryObject$inboundSchema.default("entry"),
    type: exports.ToolExecutionEntryType$inboundSchema.default("tool.execution"),
    created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
        .optional(),
    completed_at: z.nullable(z.string().datetime({ offset: true }).transform(v => new Date(v))).optional(),
    id: z.string().optional(),
    name: builtinconnectors_js_1.BuiltInConnectors$inboundSchema,
    info: z.record(z.any()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "created_at": "createdAt",
        "completed_at": "completedAt",
    });
});
/** @internal */
exports.ToolExecutionEntry$outboundSchema = z.object({
    object: exports.ToolExecutionEntryObject$outboundSchema.default("entry"),
    type: exports.ToolExecutionEntryType$outboundSchema.default("tool.execution"),
    createdAt: z.date().transform(v => v.toISOString()).optional(),
    completedAt: z.nullable(z.date().transform(v => v.toISOString())).optional(),
    id: z.string().optional(),
    name: builtinconnectors_js_1.BuiltInConnectors$outboundSchema,
    info: z.record(z.any()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        createdAt: "created_at",
        completedAt: "completed_at",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ToolExecutionEntry$;
(function (ToolExecutionEntry$) {
    /** @deprecated use `ToolExecutionEntry$inboundSchema` instead. */
    ToolExecutionEntry$.inboundSchema = exports.ToolExecutionEntry$inboundSchema;
    /** @deprecated use `ToolExecutionEntry$outboundSchema` instead. */
    ToolExecutionEntry$.outboundSchema = exports.ToolExecutionEntry$outboundSchema;
})(ToolExecutionEntry$ || (exports.ToolExecutionEntry$ = ToolExecutionEntry$ = {}));
function toolExecutionEntryToJSON(toolExecutionEntry) {
    return JSON.stringify(exports.ToolExecutionEntry$outboundSchema.parse(toolExecutionEntry));
}
function toolExecutionEntryFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ToolExecutionEntry$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ToolExecutionEntry' from JSON`);
}
//# sourceMappingURL=toolexecutionentry.js.map