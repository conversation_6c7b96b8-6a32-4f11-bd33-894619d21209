"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompletionTrainingParameters$ = exports.CompletionTrainingParameters$outboundSchema = exports.CompletionTrainingParameters$inboundSchema = void 0;
exports.completionTrainingParametersToJSON = completionTrainingParametersToJSON;
exports.completionTrainingParametersFromJSON = completionTrainingParametersFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.CompletionTrainingParameters$inboundSchema = z.object({
    training_steps: z.nullable(z.number().int()).optional(),
    learning_rate: z.number().default(0.0001),
    weight_decay: z.nullable(z.number()).optional(),
    warmup_fraction: z.nullable(z.number()).optional(),
    epochs: z.nullable(z.number()).optional(),
    seq_len: z.nullable(z.number().int()).optional(),
    fim_ratio: z.nullable(z.number()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "training_steps": "trainingSteps",
        "learning_rate": "learningRate",
        "weight_decay": "weightDecay",
        "warmup_fraction": "warmupFraction",
        "seq_len": "seqLen",
        "fim_ratio": "fimRatio",
    });
});
/** @internal */
exports.CompletionTrainingParameters$outboundSchema = z.object({
    trainingSteps: z.nullable(z.number().int()).optional(),
    learningRate: z.number().default(0.0001),
    weightDecay: z.nullable(z.number()).optional(),
    warmupFraction: z.nullable(z.number()).optional(),
    epochs: z.nullable(z.number()).optional(),
    seqLen: z.nullable(z.number().int()).optional(),
    fimRatio: z.nullable(z.number()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        trainingSteps: "training_steps",
        learningRate: "learning_rate",
        weightDecay: "weight_decay",
        warmupFraction: "warmup_fraction",
        seqLen: "seq_len",
        fimRatio: "fim_ratio",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var CompletionTrainingParameters$;
(function (CompletionTrainingParameters$) {
    /** @deprecated use `CompletionTrainingParameters$inboundSchema` instead. */
    CompletionTrainingParameters$.inboundSchema = exports.CompletionTrainingParameters$inboundSchema;
    /** @deprecated use `CompletionTrainingParameters$outboundSchema` instead. */
    CompletionTrainingParameters$.outboundSchema = exports.CompletionTrainingParameters$outboundSchema;
})(CompletionTrainingParameters$ || (exports.CompletionTrainingParameters$ = CompletionTrainingParameters$ = {}));
function completionTrainingParametersToJSON(completionTrainingParameters) {
    return JSON.stringify(exports.CompletionTrainingParameters$outboundSchema.parse(completionTrainingParameters));
}
function completionTrainingParametersFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.CompletionTrainingParameters$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'CompletionTrainingParameters' from JSON`);
}
//# sourceMappingURL=completiontrainingparameters.js.map