"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentURLChunk$ = exports.DocumentURLChunk$outboundSchema = exports.DocumentURLChunk$inboundSchema = exports.DocumentURLChunkType$ = exports.DocumentURLChunkType$outboundSchema = exports.DocumentURLChunkType$inboundSchema = exports.DocumentURLChunkType = void 0;
exports.documentURLChunkToJSON = documentURLChunkToJSON;
exports.documentURLChunkFromJSON = documentURLChunkFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
exports.DocumentURLChunkType = {
    DocumentUrl: "document_url",
};
/** @internal */
exports.DocumentURLChunkType$inboundSchema = z.nativeEnum(exports.DocumentURLChunkType);
/** @internal */
exports.DocumentURLChunkType$outboundSchema = exports.DocumentURLChunkType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var DocumentURLChunkType$;
(function (DocumentURLChunkType$) {
    /** @deprecated use `DocumentURLChunkType$inboundSchema` instead. */
    DocumentURLChunkType$.inboundSchema = exports.DocumentURLChunkType$inboundSchema;
    /** @deprecated use `DocumentURLChunkType$outboundSchema` instead. */
    DocumentURLChunkType$.outboundSchema = exports.DocumentURLChunkType$outboundSchema;
})(DocumentURLChunkType$ || (exports.DocumentURLChunkType$ = DocumentURLChunkType$ = {}));
/** @internal */
exports.DocumentURLChunk$inboundSchema = z.object({
    document_url: z.string(),
    document_name: z.nullable(z.string()).optional(),
    type: exports.DocumentURLChunkType$inboundSchema.default("document_url"),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "document_url": "documentUrl",
        "document_name": "documentName",
    });
});
/** @internal */
exports.DocumentURLChunk$outboundSchema = z.object({
    documentUrl: z.string(),
    documentName: z.nullable(z.string()).optional(),
    type: exports.DocumentURLChunkType$outboundSchema.default("document_url"),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        documentUrl: "document_url",
        documentName: "document_name",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var DocumentURLChunk$;
(function (DocumentURLChunk$) {
    /** @deprecated use `DocumentURLChunk$inboundSchema` instead. */
    DocumentURLChunk$.inboundSchema = exports.DocumentURLChunk$inboundSchema;
    /** @deprecated use `DocumentURLChunk$outboundSchema` instead. */
    DocumentURLChunk$.outboundSchema = exports.DocumentURLChunk$outboundSchema;
})(DocumentURLChunk$ || (exports.DocumentURLChunk$ = DocumentURLChunk$ = {}));
function documentURLChunkToJSON(documentURLChunk) {
    return JSON.stringify(exports.DocumentURLChunk$outboundSchema.parse(documentURLChunk));
}
function documentURLChunkFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.DocumentURLChunk$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'DocumentURLChunk' from JSON`);
}
//# sourceMappingURL=documenturlchunk.js.map