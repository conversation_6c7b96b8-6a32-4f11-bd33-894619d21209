{"version": 3, "file": "chatcompletionstreamrequest.js", "sourceRoot": "", "sources": ["../../src/models/components/chatcompletionstreamrequest.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;AAwKH,sFAQC;AAED,0FAQC;AAwED,8FAQC;AAED,kGASC;AAoCD,kGAQC;AAED,sGASC;AA0KD,8EAQC;AAED,kFAQC;AAtgBD,uCAAyB;AACzB,2DAA0D;AAC1D,qDAAiD;AAGjD,+DAK+B;AAC/B,iEAIgC;AAChC,mDAKyB;AACzB,2DAK6B;AAC7B,yDAK4B;AAC5B,uCAKmB;AACnB,mDAKyB;AACzB,2DAI6B;AAC7B,qDAK0B;AAC1B,qDAK0B;AA0E1B,gBAAgB;AACH,QAAA,6CAA6C,GAItD,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;AAK/C,gBAAgB;AACH,QAAA,8CAA8C,GAIvD,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;AAE/C;;;GAGG;AACH,IAAiB,gCAAgC,CAOhD;AAPD,WAAiB,gCAAgC;IAC/C,+EAA+E;IAClE,8CAAa,GAAG,qDAA6C,CAAC;IAC3E,gFAAgF;IACnE,+CAAc,GAAG,sDAA8C,CAAC;AAG/E,CAAC,EAPgB,gCAAgC,gDAAhC,gCAAgC,QAOhD;AAED,SAAgB,qCAAqC,CACnD,+BAAgE;IAEhE,OAAO,IAAI,CAAC,SAAS,CACnB,sDAA8C,CAAC,KAAK,CAClD,+BAA+B,CAChC,CACF,CAAC;AACJ,CAAC;AAED,SAAgB,uCAAuC,CACrD,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,qDAA6C,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACzE,6DAA6D,CAC9D,CAAC;AACJ,CAAC;AAED,gBAAgB;AACH,QAAA,iDAAiD,GAI1D,CAAC,CAAC,KAAK,CAAC;IACV,8CAA2B,CAAC,GAAG,CAC7B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACxD,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,0CAAyB,CAAC,GAAG,CAC3B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAC3E;IACD,oDAA8B,CAAC,GAAG,CAChC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,0CAAyB,CAAC,GAAG,CAC3B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAC3E;CACF,CAAC,CAAC;AASH,gBAAgB;AACH,QAAA,kDAAkD,GAI3D,CAAC,CAAC,KAAK,CAAC;IACV,+CAA4B,CAAC,GAAG,CAC9B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACxD,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,2CAA0B,CAAC,GAAG,CAC5B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAC3E;IACD,qDAA+B,CAAC,GAAG,CACjC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,2CAA0B,CAAC,GAAG,CAC5B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAC3E;CACF,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,oCAAoC,CASpD;AATD,WAAiB,oCAAoC;IACnD,mFAAmF;IACtE,kDAAa,GACxB,yDAAiD,CAAC;IACpD,oFAAoF;IACvE,mDAAc,GACzB,0DAAkD,CAAC;AAGvD,CAAC,EATgB,oCAAoC,oDAApC,oCAAoC,QASpD;AAED,SAAgB,yCAAyC,CACvD,mCAAwE;IAExE,OAAO,IAAI,CAAC,SAAS,CACnB,0DAAkD,CAAC,KAAK,CACtD,mCAAmC,CACpC,CACF,CAAC;AACJ,CAAC;AAED,SAAgB,2CAA2C,CACzD,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CACJ,yDAAiD,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACxE,iEAAiE,CAClE,CAAC;AACJ,CAAC;AAED,gBAAgB;AACH,QAAA,mDAAmD,GAI5D,CAAC,CAAC,KAAK,CAAC,CAAC,wCAAwB,EAAE,gDAA4B,CAAC,CAAC,CAAC;AAOtE,gBAAgB;AACH,QAAA,oDAAoD,GAI7D,CAAC,CAAC,KAAK,CAAC,CAAC,yCAAyB,EAAE,iDAA6B,CAAC,CAAC,CAAC;AAExE;;;GAGG;AACH,IAAiB,sCAAsC,CAStD;AATD,WAAiB,sCAAsC;IACrD,qFAAqF;IACxE,oDAAa,GACxB,2DAAmD,CAAC;IACtD,sFAAsF;IACzE,qDAAc,GACzB,4DAAoD,CAAC;AAGzD,CAAC,EATgB,sCAAsC,sDAAtC,sCAAsC,QAStD;AAED,SAAgB,2CAA2C,CACzD,qCAA4E;IAE5E,OAAO,IAAI,CAAC,SAAS,CACnB,4DAAoD,CAAC,KAAK,CACxD,qCAAqC,CACtC,CACF,CAAC;AACJ,CAAC;AAED,SAAgB,6CAA6C,CAC3D,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CACJ,2DAAmD,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC1E,mEAAmE,CACpE,CAAC;AACJ,CAAC;AAED,gBAAgB;AACH,QAAA,yCAAyC,GAIlD,CAAC,CAAC,MAAM,CAAC;IACX,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE;IACjB,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC9C,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IACnD,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACjC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC3D,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IACpD,QAAQ,EAAE,CAAC,CAAC,KAAK,CACf,CAAC,CAAC,KAAK,CAAC;QACN,8CAA2B,CAAC,GAAG,CAC7B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACxD,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,0CAAyB,CAAC,GAAG,CAC3B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACtD,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,oDAA8B,CAAC,GAAG,CAChC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC3D,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,0CAAyB,CAAC,GAAG,CAC3B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACtD,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;KACF,CAAC,CACH;IACD,eAAe,EAAE,gDAA4B,CAAC,QAAQ,EAAE;IACxD,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,4BAAkB,CAAC,CAAC,CAAC,QAAQ,EAAE;IACzD,WAAW,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,wCAAwB,EAAE,gDAA4B,CAAC,CAAC;SAC3E,QAAQ,EAAE;IACb,gBAAgB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACvC,iBAAiB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACxC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC1C,UAAU,EAAE,wCAAwB,CAAC,QAAQ,EAAE;IAC/C,mBAAmB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;IAC3C,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,sDAA+B,CAAC,CAAC,QAAQ,EAAE;IACnE,WAAW,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;CACpC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,OAAO,EAAE,MAAM;QACf,YAAY,EAAE,WAAW;QACzB,aAAa,EAAE,YAAY;QAC3B,iBAAiB,EAAE,gBAAgB;QACnC,aAAa,EAAE,YAAY;QAC3B,kBAAkB,EAAE,iBAAiB;QACrC,mBAAmB,EAAE,kBAAkB;QACvC,qBAAqB,EAAE,mBAAmB;QAC1C,aAAa,EAAE,YAAY;QAC3B,aAAa,EAAE,YAAY;KAC5B,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AA6BH,gBAAgB;AACH,QAAA,0CAA0C,GAInD,CAAC,CAAC,MAAM,CAAC;IACX,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE;IACjB,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC9C,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC3B,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IAClD,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACjC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC3D,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IACnD,QAAQ,EAAE,CAAC,CAAC,KAAK,CACf,CAAC,CAAC,KAAK,CAAC;QACN,+CAA4B,CAAC,GAAG,CAC9B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACxD,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,2CAA0B,CAAC,GAAG,CAC5B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACtD,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,qDAA+B,CAAC,GAAG,CACjC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC3D,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,2CAA0B,CAAC,GAAG,CAC5B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACtD,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;KACF,CAAC,CACH;IACD,cAAc,EAAE,iDAA6B,CAAC,QAAQ,EAAE;IACxD,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,6BAAmB,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC1D,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC;QAClB,yCAAyB;QACzB,iDAA6B;KAC9B,CAAC,CAAC,QAAQ,EAAE;IACb,eAAe,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACtC,gBAAgB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACvC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC1C,UAAU,EAAE,yCAAyB,CAAC,QAAQ,EAAE;IAChD,iBAAiB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;IACzC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,uDAAgC,CAAC,CAAC,QAAQ,EAAE;IACnE,UAAU,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;CACnC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,IAAI,EAAE,OAAO;QACb,SAAS,EAAE,YAAY;QACvB,UAAU,EAAE,aAAa;QACzB,cAAc,EAAE,iBAAiB;QACjC,UAAU,EAAE,aAAa;QACzB,eAAe,EAAE,kBAAkB;QACnC,gBAAgB,EAAE,mBAAmB;QACrC,iBAAiB,EAAE,qBAAqB;QACxC,UAAU,EAAE,aAAa;QACzB,UAAU,EAAE,aAAa;KAC1B,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,4BAA4B,CAO5C;AAPD,WAAiB,4BAA4B;IAC3C,2EAA2E;IAC9D,0CAAa,GAAG,iDAAyC,CAAC;IACvE,4EAA4E;IAC/D,2CAAc,GAAG,kDAA0C,CAAC;AAG3E,CAAC,EAPgB,4BAA4B,4CAA5B,4BAA4B,QAO5C;AAED,SAAgB,iCAAiC,CAC/C,2BAAwD;IAExD,OAAO,IAAI,CAAC,SAAS,CACnB,kDAA0C,CAAC,KAAK,CAC9C,2BAA2B,CAC5B,CACF,CAAC;AACJ,CAAC;AAED,SAAgB,mCAAmC,CACjD,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,iDAAyC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACrE,yDAAyD,CAC1D,CAAC;AACJ,CAAC"}