"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReferenceChunk$ = exports.ReferenceChunk$outboundSchema = exports.ReferenceChunk$inboundSchema = exports.ReferenceChunkType$ = exports.ReferenceChunkType$outboundSchema = exports.ReferenceChunkType$inboundSchema = exports.ReferenceChunkType = void 0;
exports.referenceChunkToJSON = referenceChunkToJSON;
exports.referenceChunkFromJSON = referenceChunkFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
exports.ReferenceChunkType = {
    Reference: "reference",
};
/** @internal */
exports.ReferenceChunkType$inboundSchema = z.nativeEnum(exports.ReferenceChunkType);
/** @internal */
exports.ReferenceChunkType$outboundSchema = exports.ReferenceChunkType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ReferenceChunkType$;
(function (ReferenceChunkType$) {
    /** @deprecated use `ReferenceChunkType$inboundSchema` instead. */
    ReferenceChunkType$.inboundSchema = exports.ReferenceChunkType$inboundSchema;
    /** @deprecated use `ReferenceChunkType$outboundSchema` instead. */
    ReferenceChunkType$.outboundSchema = exports.ReferenceChunkType$outboundSchema;
})(ReferenceChunkType$ || (exports.ReferenceChunkType$ = ReferenceChunkType$ = {}));
/** @internal */
exports.ReferenceChunk$inboundSchema = z.object({
    reference_ids: z.array(z.number().int()),
    type: exports.ReferenceChunkType$inboundSchema.default("reference"),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "reference_ids": "referenceIds",
    });
});
/** @internal */
exports.ReferenceChunk$outboundSchema = z.object({
    referenceIds: z.array(z.number().int()),
    type: exports.ReferenceChunkType$outboundSchema.default("reference"),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        referenceIds: "reference_ids",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ReferenceChunk$;
(function (ReferenceChunk$) {
    /** @deprecated use `ReferenceChunk$inboundSchema` instead. */
    ReferenceChunk$.inboundSchema = exports.ReferenceChunk$inboundSchema;
    /** @deprecated use `ReferenceChunk$outboundSchema` instead. */
    ReferenceChunk$.outboundSchema = exports.ReferenceChunk$outboundSchema;
})(ReferenceChunk$ || (exports.ReferenceChunk$ = ReferenceChunk$ = {}));
function referenceChunkToJSON(referenceChunk) {
    return JSON.stringify(exports.ReferenceChunk$outboundSchema.parse(referenceChunk));
}
function referenceChunkFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ReferenceChunk$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ReferenceChunk' from JSON`);
}
//# sourceMappingURL=referencechunk.js.map