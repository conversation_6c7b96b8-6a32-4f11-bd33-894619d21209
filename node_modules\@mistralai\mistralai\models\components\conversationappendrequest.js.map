{"version": 3, "file": "conversationappendrequest.js", "sourceRoot": "", "sources": ["../../src/models/components/conversationappendrequest.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;AA8HH,0EAMC;AAED,8EAQC;AA5ID,uCAAyB;AACzB,2DAA0D;AAC1D,qDAAiD;AAIjD,2DAK6B;AAC7B,mEAKiC;AAEpB,QAAA,yCAAyC,GAAG;IACvD,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,QAAQ;CACR,CAAC;AAmBX,gBAAgB;AACH,QAAA,uDAAuD,GACE,CAAC;KAClE,UAAU,CAAC,iDAAyC,CAAC,CAAC;AAE3D,gBAAgB;AACH,QAAA,wDAAwD,GAEjE,+DAAuD,CAAC;AAE5D;;;GAGG;AACH,IAAiB,0CAA0C,CAO1D;AAPD,WAAiB,0CAA0C;IACzD,yFAAyF;IAC5E,wDAAa,GACxB,+DAAuD,CAAC;IAC1D,0FAA0F;IAC7E,yDAAc,GACzB,gEAAwD,CAAC;AAC7D,CAAC,EAPgB,0CAA0C,0DAA1C,0CAA0C,QAO1D;AAED,gBAAgB;AACH,QAAA,uCAAuC,GAIhD,CAAC,CAAC,MAAM,CAAC;IACX,MAAM,EAAE,wDAAgC;IACxC,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAClC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IAChC,iBAAiB,EAAE,+DAAuD;SACvE,OAAO,CAAC,QAAQ,CAAC;IACpB,eAAe,EAAE,gDAA4B,CAAC,QAAQ,EAAE;CACzD,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,mBAAmB,EAAE,kBAAkB;QACvC,iBAAiB,EAAE,gBAAgB;KACpC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAWH,gBAAgB;AACH,QAAA,wCAAwC,GAIjD,CAAC,CAAC,MAAM,CAAC;IACX,MAAM,EAAE,yDAAiC;IACzC,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAClC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IAChC,gBAAgB,EAAE,gEAAwD;SACvE,OAAO,CAAC,QAAQ,CAAC;IACpB,cAAc,EAAE,iDAA6B,CAAC,QAAQ,EAAE;CACzD,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,gBAAgB,EAAE,mBAAmB;QACrC,cAAc,EAAE,iBAAiB;KAClC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,0BAA0B,CAO1C;AAPD,WAAiB,0BAA0B;IACzC,yEAAyE;IAC5D,wCAAa,GAAG,+CAAuC,CAAC;IACrE,0EAA0E;IAC7D,yCAAc,GAAG,gDAAwC,CAAC;AAGzE,CAAC,EAPgB,0BAA0B,0CAA1B,0BAA0B,QAO1C;AAED,SAAgB,+BAA+B,CAC7C,yBAAoD;IAEpD,OAAO,IAAI,CAAC,SAAS,CACnB,gDAAwC,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAC1E,CAAC;AACJ,CAAC;AAED,SAAgB,iCAAiC,CAC/C,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,+CAAuC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACnE,uDAAuD,CACxD,CAAC;AACJ,CAAC"}