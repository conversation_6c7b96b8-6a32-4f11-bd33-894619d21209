"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageOutputEntry$ = exports.MessageOutputEntry$outboundSchema = exports.MessageOutputEntry$inboundSchema = exports.MessageOutputEntryContent$ = exports.MessageOutputEntryContent$outboundSchema = exports.MessageOutputEntryContent$inboundSchema = exports.MessageOutputEntryRole$ = exports.MessageOutputEntryRole$outboundSchema = exports.MessageOutputEntryRole$inboundSchema = exports.MessageOutputEntryType$ = exports.MessageOutputEntryType$outboundSchema = exports.MessageOutputEntryType$inboundSchema = exports.MessageOutputEntryObject$ = exports.MessageOutputEntryObject$outboundSchema = exports.MessageOutputEntryObject$inboundSchema = exports.MessageOutputEntryRole = exports.MessageOutputEntryType = exports.MessageOutputEntryObject = void 0;
exports.messageOutputEntryContentToJSON = messageOutputEntryContentToJSON;
exports.messageOutputEntryContentFromJSON = messageOutputEntryContentFromJSON;
exports.messageOutputEntryToJSON = messageOutputEntryToJSON;
exports.messageOutputEntryFromJSON = messageOutputEntryFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const messageoutputcontentchunks_js_1 = require("./messageoutputcontentchunks.js");
exports.MessageOutputEntryObject = {
    Entry: "entry",
};
exports.MessageOutputEntryType = {
    MessageOutput: "message.output",
};
exports.MessageOutputEntryRole = {
    Assistant: "assistant",
};
/** @internal */
exports.MessageOutputEntryObject$inboundSchema = z.nativeEnum(exports.MessageOutputEntryObject);
/** @internal */
exports.MessageOutputEntryObject$outboundSchema = exports.MessageOutputEntryObject$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MessageOutputEntryObject$;
(function (MessageOutputEntryObject$) {
    /** @deprecated use `MessageOutputEntryObject$inboundSchema` instead. */
    MessageOutputEntryObject$.inboundSchema = exports.MessageOutputEntryObject$inboundSchema;
    /** @deprecated use `MessageOutputEntryObject$outboundSchema` instead. */
    MessageOutputEntryObject$.outboundSchema = exports.MessageOutputEntryObject$outboundSchema;
})(MessageOutputEntryObject$ || (exports.MessageOutputEntryObject$ = MessageOutputEntryObject$ = {}));
/** @internal */
exports.MessageOutputEntryType$inboundSchema = z.nativeEnum(exports.MessageOutputEntryType);
/** @internal */
exports.MessageOutputEntryType$outboundSchema = exports.MessageOutputEntryType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MessageOutputEntryType$;
(function (MessageOutputEntryType$) {
    /** @deprecated use `MessageOutputEntryType$inboundSchema` instead. */
    MessageOutputEntryType$.inboundSchema = exports.MessageOutputEntryType$inboundSchema;
    /** @deprecated use `MessageOutputEntryType$outboundSchema` instead. */
    MessageOutputEntryType$.outboundSchema = exports.MessageOutputEntryType$outboundSchema;
})(MessageOutputEntryType$ || (exports.MessageOutputEntryType$ = MessageOutputEntryType$ = {}));
/** @internal */
exports.MessageOutputEntryRole$inboundSchema = z.nativeEnum(exports.MessageOutputEntryRole);
/** @internal */
exports.MessageOutputEntryRole$outboundSchema = exports.MessageOutputEntryRole$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MessageOutputEntryRole$;
(function (MessageOutputEntryRole$) {
    /** @deprecated use `MessageOutputEntryRole$inboundSchema` instead. */
    MessageOutputEntryRole$.inboundSchema = exports.MessageOutputEntryRole$inboundSchema;
    /** @deprecated use `MessageOutputEntryRole$outboundSchema` instead. */
    MessageOutputEntryRole$.outboundSchema = exports.MessageOutputEntryRole$outboundSchema;
})(MessageOutputEntryRole$ || (exports.MessageOutputEntryRole$ = MessageOutputEntryRole$ = {}));
/** @internal */
exports.MessageOutputEntryContent$inboundSchema = z.union([z.string(), z.array(messageoutputcontentchunks_js_1.MessageOutputContentChunks$inboundSchema)]);
/** @internal */
exports.MessageOutputEntryContent$outboundSchema = z.union([z.string(), z.array(messageoutputcontentchunks_js_1.MessageOutputContentChunks$outboundSchema)]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MessageOutputEntryContent$;
(function (MessageOutputEntryContent$) {
    /** @deprecated use `MessageOutputEntryContent$inboundSchema` instead. */
    MessageOutputEntryContent$.inboundSchema = exports.MessageOutputEntryContent$inboundSchema;
    /** @deprecated use `MessageOutputEntryContent$outboundSchema` instead. */
    MessageOutputEntryContent$.outboundSchema = exports.MessageOutputEntryContent$outboundSchema;
})(MessageOutputEntryContent$ || (exports.MessageOutputEntryContent$ = MessageOutputEntryContent$ = {}));
function messageOutputEntryContentToJSON(messageOutputEntryContent) {
    return JSON.stringify(exports.MessageOutputEntryContent$outboundSchema.parse(messageOutputEntryContent));
}
function messageOutputEntryContentFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.MessageOutputEntryContent$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'MessageOutputEntryContent' from JSON`);
}
/** @internal */
exports.MessageOutputEntry$inboundSchema = z.object({
    object: exports.MessageOutputEntryObject$inboundSchema.default("entry"),
    type: exports.MessageOutputEntryType$inboundSchema.default("message.output"),
    created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
        .optional(),
    completed_at: z.nullable(z.string().datetime({ offset: true }).transform(v => new Date(v))).optional(),
    id: z.string().optional(),
    agent_id: z.nullable(z.string()).optional(),
    model: z.nullable(z.string()).optional(),
    role: exports.MessageOutputEntryRole$inboundSchema.default("assistant"),
    content: z.union([
        z.string(),
        z.array(messageoutputcontentchunks_js_1.MessageOutputContentChunks$inboundSchema),
    ]),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "created_at": "createdAt",
        "completed_at": "completedAt",
        "agent_id": "agentId",
    });
});
/** @internal */
exports.MessageOutputEntry$outboundSchema = z.object({
    object: exports.MessageOutputEntryObject$outboundSchema.default("entry"),
    type: exports.MessageOutputEntryType$outboundSchema.default("message.output"),
    createdAt: z.date().transform(v => v.toISOString()).optional(),
    completedAt: z.nullable(z.date().transform(v => v.toISOString())).optional(),
    id: z.string().optional(),
    agentId: z.nullable(z.string()).optional(),
    model: z.nullable(z.string()).optional(),
    role: exports.MessageOutputEntryRole$outboundSchema.default("assistant"),
    content: z.union([
        z.string(),
        z.array(messageoutputcontentchunks_js_1.MessageOutputContentChunks$outboundSchema),
    ]),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        createdAt: "created_at",
        completedAt: "completed_at",
        agentId: "agent_id",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MessageOutputEntry$;
(function (MessageOutputEntry$) {
    /** @deprecated use `MessageOutputEntry$inboundSchema` instead. */
    MessageOutputEntry$.inboundSchema = exports.MessageOutputEntry$inboundSchema;
    /** @deprecated use `MessageOutputEntry$outboundSchema` instead. */
    MessageOutputEntry$.outboundSchema = exports.MessageOutputEntry$outboundSchema;
})(MessageOutputEntry$ || (exports.MessageOutputEntry$ = MessageOutputEntry$ = {}));
function messageOutputEntryToJSON(messageOutputEntry) {
    return JSON.stringify(exports.MessageOutputEntry$outboundSchema.parse(messageOutputEntry));
}
function messageOutputEntryFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.MessageOutputEntry$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'MessageOutputEntry' from JSON`);
}
//# sourceMappingURL=messageoutputentry.js.map