"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationStreamRequest$ = exports.ConversationStreamRequest$outboundSchema = exports.ConversationStreamRequest$inboundSchema = exports.ConversationStreamRequestTools$ = exports.ConversationStreamRequestTools$outboundSchema = exports.ConversationStreamRequestTools$inboundSchema = exports.ConversationStreamRequestHandoffExecution$ = exports.ConversationStreamRequestHandoffExecution$outboundSchema = exports.ConversationStreamRequestHandoffExecution$inboundSchema = exports.ConversationStreamRequestHandoffExecution = void 0;
exports.conversationStreamRequestToolsToJSON = conversationStreamRequestToolsToJSON;
exports.conversationStreamRequestToolsFromJSON = conversationStreamRequestToolsFromJSON;
exports.conversationStreamRequestToJSON = conversationStreamRequestToJSON;
exports.conversationStreamRequestFromJSON = conversationStreamRequestFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const codeinterpretertool_js_1 = require("./codeinterpretertool.js");
const completionargs_js_1 = require("./completionargs.js");
const conversationinputs_js_1 = require("./conversationinputs.js");
const documentlibrarytool_js_1 = require("./documentlibrarytool.js");
const functiontool_js_1 = require("./functiontool.js");
const imagegenerationtool_js_1 = require("./imagegenerationtool.js");
const websearchpremiumtool_js_1 = require("./websearchpremiumtool.js");
const websearchtool_js_1 = require("./websearchtool.js");
exports.ConversationStreamRequestHandoffExecution = {
    Client: "client",
    Server: "server",
};
/** @internal */
exports.ConversationStreamRequestHandoffExecution$inboundSchema = z
    .nativeEnum(exports.ConversationStreamRequestHandoffExecution);
/** @internal */
exports.ConversationStreamRequestHandoffExecution$outboundSchema = exports.ConversationStreamRequestHandoffExecution$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationStreamRequestHandoffExecution$;
(function (ConversationStreamRequestHandoffExecution$) {
    /** @deprecated use `ConversationStreamRequestHandoffExecution$inboundSchema` instead. */
    ConversationStreamRequestHandoffExecution$.inboundSchema = exports.ConversationStreamRequestHandoffExecution$inboundSchema;
    /** @deprecated use `ConversationStreamRequestHandoffExecution$outboundSchema` instead. */
    ConversationStreamRequestHandoffExecution$.outboundSchema = exports.ConversationStreamRequestHandoffExecution$outboundSchema;
})(ConversationStreamRequestHandoffExecution$ || (exports.ConversationStreamRequestHandoffExecution$ = ConversationStreamRequestHandoffExecution$ = {}));
/** @internal */
exports.ConversationStreamRequestTools$inboundSchema = z.union([
    codeinterpretertool_js_1.CodeInterpreterTool$inboundSchema.and(z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
        type: v.type,
    }))),
    imagegenerationtool_js_1.ImageGenerationTool$inboundSchema.and(z.object({ type: z.literal("image_generation") }).transform((v) => ({
        type: v.type,
    }))),
    websearchtool_js_1.WebSearchTool$inboundSchema.and(z.object({ type: z.literal("web_search") }).transform((v) => ({
        type: v.type,
    }))),
    websearchpremiumtool_js_1.WebSearchPremiumTool$inboundSchema.and(z.object({ type: z.literal("web_search_premium") }).transform((v) => ({
        type: v.type,
    }))),
    documentlibrarytool_js_1.DocumentLibraryTool$inboundSchema.and(z.object({ type: z.literal("document_library") }).transform((v) => ({
        type: v.type,
    }))),
    functiontool_js_1.FunctionTool$inboundSchema.and(z.object({ type: z.literal("function") }).transform((v) => ({
        type: v.type,
    }))),
]);
/** @internal */
exports.ConversationStreamRequestTools$outboundSchema = z.union([
    codeinterpretertool_js_1.CodeInterpreterTool$outboundSchema.and(z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
        type: v.type,
    }))),
    imagegenerationtool_js_1.ImageGenerationTool$outboundSchema.and(z.object({ type: z.literal("image_generation") }).transform((v) => ({
        type: v.type,
    }))),
    websearchtool_js_1.WebSearchTool$outboundSchema.and(z.object({ type: z.literal("web_search") }).transform((v) => ({
        type: v.type,
    }))),
    websearchpremiumtool_js_1.WebSearchPremiumTool$outboundSchema.and(z.object({ type: z.literal("web_search_premium") }).transform((v) => ({
        type: v.type,
    }))),
    documentlibrarytool_js_1.DocumentLibraryTool$outboundSchema.and(z.object({ type: z.literal("document_library") }).transform((v) => ({
        type: v.type,
    }))),
    functiontool_js_1.FunctionTool$outboundSchema.and(z.object({ type: z.literal("function") }).transform((v) => ({
        type: v.type,
    }))),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationStreamRequestTools$;
(function (ConversationStreamRequestTools$) {
    /** @deprecated use `ConversationStreamRequestTools$inboundSchema` instead. */
    ConversationStreamRequestTools$.inboundSchema = exports.ConversationStreamRequestTools$inboundSchema;
    /** @deprecated use `ConversationStreamRequestTools$outboundSchema` instead. */
    ConversationStreamRequestTools$.outboundSchema = exports.ConversationStreamRequestTools$outboundSchema;
})(ConversationStreamRequestTools$ || (exports.ConversationStreamRequestTools$ = ConversationStreamRequestTools$ = {}));
function conversationStreamRequestToolsToJSON(conversationStreamRequestTools) {
    return JSON.stringify(exports.ConversationStreamRequestTools$outboundSchema.parse(conversationStreamRequestTools));
}
function conversationStreamRequestToolsFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ConversationStreamRequestTools$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ConversationStreamRequestTools' from JSON`);
}
/** @internal */
exports.ConversationStreamRequest$inboundSchema = z.object({
    inputs: conversationinputs_js_1.ConversationInputs$inboundSchema,
    stream: z.boolean().default(true),
    store: z.nullable(z.boolean()).optional(),
    handoff_execution: z.nullable(exports.ConversationStreamRequestHandoffExecution$inboundSchema).optional(),
    instructions: z.nullable(z.string()).optional(),
    tools: z.nullable(z.array(z.union([
        codeinterpretertool_js_1.CodeInterpreterTool$inboundSchema.and(z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
            type: v.type,
        }))),
        imagegenerationtool_js_1.ImageGenerationTool$inboundSchema.and(z.object({ type: z.literal("image_generation") }).transform((v) => ({
            type: v.type,
        }))),
        websearchtool_js_1.WebSearchTool$inboundSchema.and(z.object({ type: z.literal("web_search") }).transform((v) => ({
            type: v.type,
        }))),
        websearchpremiumtool_js_1.WebSearchPremiumTool$inboundSchema.and(z.object({ type: z.literal("web_search_premium") }).transform((v) => ({ type: v.type }))),
        documentlibrarytool_js_1.DocumentLibraryTool$inboundSchema.and(z.object({ type: z.literal("document_library") }).transform((v) => ({
            type: v.type,
        }))),
        functiontool_js_1.FunctionTool$inboundSchema.and(z.object({ type: z.literal("function") }).transform((v) => ({
            type: v.type,
        }))),
    ]))).optional(),
    completion_args: z.nullable(completionargs_js_1.CompletionArgs$inboundSchema).optional(),
    name: z.nullable(z.string()).optional(),
    description: z.nullable(z.string()).optional(),
    agent_id: z.nullable(z.string()).optional(),
    model: z.nullable(z.string()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "handoff_execution": "handoffExecution",
        "completion_args": "completionArgs",
        "agent_id": "agentId",
    });
});
/** @internal */
exports.ConversationStreamRequest$outboundSchema = z.object({
    inputs: conversationinputs_js_1.ConversationInputs$outboundSchema,
    stream: z.boolean().default(true),
    store: z.nullable(z.boolean()).optional(),
    handoffExecution: z.nullable(exports.ConversationStreamRequestHandoffExecution$outboundSchema).optional(),
    instructions: z.nullable(z.string()).optional(),
    tools: z.nullable(z.array(z.union([
        codeinterpretertool_js_1.CodeInterpreterTool$outboundSchema.and(z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
            type: v.type,
        }))),
        imagegenerationtool_js_1.ImageGenerationTool$outboundSchema.and(z.object({ type: z.literal("image_generation") }).transform((v) => ({
            type: v.type,
        }))),
        websearchtool_js_1.WebSearchTool$outboundSchema.and(z.object({ type: z.literal("web_search") }).transform((v) => ({
            type: v.type,
        }))),
        websearchpremiumtool_js_1.WebSearchPremiumTool$outboundSchema.and(z.object({ type: z.literal("web_search_premium") }).transform((v) => ({ type: v.type }))),
        documentlibrarytool_js_1.DocumentLibraryTool$outboundSchema.and(z.object({ type: z.literal("document_library") }).transform((v) => ({
            type: v.type,
        }))),
        functiontool_js_1.FunctionTool$outboundSchema.and(z.object({ type: z.literal("function") }).transform((v) => ({
            type: v.type,
        }))),
    ]))).optional(),
    completionArgs: z.nullable(completionargs_js_1.CompletionArgs$outboundSchema).optional(),
    name: z.nullable(z.string()).optional(),
    description: z.nullable(z.string()).optional(),
    agentId: z.nullable(z.string()).optional(),
    model: z.nullable(z.string()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        handoffExecution: "handoff_execution",
        completionArgs: "completion_args",
        agentId: "agent_id",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ConversationStreamRequest$;
(function (ConversationStreamRequest$) {
    /** @deprecated use `ConversationStreamRequest$inboundSchema` instead. */
    ConversationStreamRequest$.inboundSchema = exports.ConversationStreamRequest$inboundSchema;
    /** @deprecated use `ConversationStreamRequest$outboundSchema` instead. */
    ConversationStreamRequest$.outboundSchema = exports.ConversationStreamRequest$outboundSchema;
})(ConversationStreamRequest$ || (exports.ConversationStreamRequest$ = ConversationStreamRequest$ = {}));
function conversationStreamRequestToJSON(conversationStreamRequest) {
    return JSON.stringify(exports.ConversationStreamRequest$outboundSchema.parse(conversationStreamRequest));
}
function conversationStreamRequestFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ConversationStreamRequest$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ConversationStreamRequest' from JSON`);
}
//# sourceMappingURL=conversationstreamrequest.js.map