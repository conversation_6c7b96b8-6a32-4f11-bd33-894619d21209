"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseStartedEvent$ = exports.ResponseStartedEvent$outboundSchema = exports.ResponseStartedEvent$inboundSchema = exports.ResponseStartedEventType$ = exports.ResponseStartedEventType$outboundSchema = exports.ResponseStartedEventType$inboundSchema = exports.ResponseStartedEventType = void 0;
exports.responseStartedEventToJSON = responseStartedEventToJSON;
exports.responseStartedEventFromJSON = responseStartedEventFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
exports.ResponseStartedEventType = {
    ConversationResponseStarted: "conversation.response.started",
};
/** @internal */
exports.ResponseStartedEventType$inboundSchema = z.nativeEnum(exports.ResponseStartedEventType);
/** @internal */
exports.ResponseStartedEventType$outboundSchema = exports.ResponseStartedEventType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ResponseStartedEventType$;
(function (ResponseStartedEventType$) {
    /** @deprecated use `ResponseStartedEventType$inboundSchema` instead. */
    ResponseStartedEventType$.inboundSchema = exports.ResponseStartedEventType$inboundSchema;
    /** @deprecated use `ResponseStartedEventType$outboundSchema` instead. */
    ResponseStartedEventType$.outboundSchema = exports.ResponseStartedEventType$outboundSchema;
})(ResponseStartedEventType$ || (exports.ResponseStartedEventType$ = ResponseStartedEventType$ = {}));
/** @internal */
exports.ResponseStartedEvent$inboundSchema = z.object({
    type: exports.ResponseStartedEventType$inboundSchema.default("conversation.response.started"),
    created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
        .optional(),
    conversation_id: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "created_at": "createdAt",
        "conversation_id": "conversationId",
    });
});
/** @internal */
exports.ResponseStartedEvent$outboundSchema = z.object({
    type: exports.ResponseStartedEventType$outboundSchema.default("conversation.response.started"),
    createdAt: z.date().transform(v => v.toISOString()).optional(),
    conversationId: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        createdAt: "created_at",
        conversationId: "conversation_id",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ResponseStartedEvent$;
(function (ResponseStartedEvent$) {
    /** @deprecated use `ResponseStartedEvent$inboundSchema` instead. */
    ResponseStartedEvent$.inboundSchema = exports.ResponseStartedEvent$inboundSchema;
    /** @deprecated use `ResponseStartedEvent$outboundSchema` instead. */
    ResponseStartedEvent$.outboundSchema = exports.ResponseStartedEvent$outboundSchema;
})(ResponseStartedEvent$ || (exports.ResponseStartedEvent$ = ResponseStartedEvent$ = {}));
function responseStartedEventToJSON(responseStartedEvent) {
    return JSON.stringify(exports.ResponseStartedEvent$outboundSchema.parse(responseStartedEvent));
}
function responseStartedEventFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ResponseStartedEvent$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ResponseStartedEvent' from JSON`);
}
//# sourceMappingURL=responsestartedevent.js.map