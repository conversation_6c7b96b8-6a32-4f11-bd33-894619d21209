"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OCRPageDimensions$ = exports.OCRPageDimensions$outboundSchema = exports.OCRPageDimensions$inboundSchema = void 0;
exports.ocrPageDimensionsToJSON = ocrPageDimensionsToJSON;
exports.ocrPageDimensionsFromJSON = ocrPageDimensionsFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.OCRPageDimensions$inboundSchema = z.object({
    dpi: z.number().int(),
    height: z.number().int(),
    width: z.number().int(),
});
/** @internal */
exports.OCRPageDimensions$outboundSchema = z.object({
    dpi: z.number().int(),
    height: z.number().int(),
    width: z.number().int(),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var OCRPageDimensions$;
(function (OCRPageDimensions$) {
    /** @deprecated use `OCRPageDimensions$inboundSchema` instead. */
    OCRPageDimensions$.inboundSchema = exports.OCRPageDimensions$inboundSchema;
    /** @deprecated use `OCRPageDimensions$outboundSchema` instead. */
    OCRPageDimensions$.outboundSchema = exports.OCRPageDimensions$outboundSchema;
})(OCRPageDimensions$ || (exports.OCRPageDimensions$ = OCRPageDimensions$ = {}));
function ocrPageDimensionsToJSON(ocrPageDimensions) {
    return JSON.stringify(exports.OCRPageDimensions$outboundSchema.parse(ocrPageDimensions));
}
function ocrPageDimensionsFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.OCRPageDimensions$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'OCRPageDimensions' from JSON`);
}
//# sourceMappingURL=ocrpagedimensions.js.map