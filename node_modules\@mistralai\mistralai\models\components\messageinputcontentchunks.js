"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageInputContentChunks$ = exports.MessageInputContentChunks$outboundSchema = exports.MessageInputContentChunks$inboundSchema = void 0;
exports.messageInputContentChunksToJSON = messageInputContentChunksToJSON;
exports.messageInputContentChunksFromJSON = messageInputContentChunksFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const documenturlchunk_js_1 = require("./documenturlchunk.js");
const imageurlchunk_js_1 = require("./imageurlchunk.js");
const textchunk_js_1 = require("./textchunk.js");
const toolfilechunk_js_1 = require("./toolfilechunk.js");
/** @internal */
exports.MessageInputContentChunks$inboundSchema = z.union([
    textchunk_js_1.TextChunk$inboundSchema,
    imageurlchunk_js_1.ImageURLChunk$inboundSchema,
    documenturlchunk_js_1.DocumentURLChunk$inboundSchema,
    toolfilechunk_js_1.ToolFileChunk$inboundSchema,
]);
/** @internal */
exports.MessageInputContentChunks$outboundSchema = z.union([
    textchunk_js_1.TextChunk$outboundSchema,
    imageurlchunk_js_1.ImageURLChunk$outboundSchema,
    documenturlchunk_js_1.DocumentURLChunk$outboundSchema,
    toolfilechunk_js_1.ToolFileChunk$outboundSchema,
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MessageInputContentChunks$;
(function (MessageInputContentChunks$) {
    /** @deprecated use `MessageInputContentChunks$inboundSchema` instead. */
    MessageInputContentChunks$.inboundSchema = exports.MessageInputContentChunks$inboundSchema;
    /** @deprecated use `MessageInputContentChunks$outboundSchema` instead. */
    MessageInputContentChunks$.outboundSchema = exports.MessageInputContentChunks$outboundSchema;
})(MessageInputContentChunks$ || (exports.MessageInputContentChunks$ = MessageInputContentChunks$ = {}));
function messageInputContentChunksToJSON(messageInputContentChunks) {
    return JSON.stringify(exports.MessageInputContentChunks$outboundSchema.parse(messageInputContentChunks));
}
function messageInputContentChunksFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.MessageInputContentChunks$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'MessageInputContentChunks' from JSON`);
}
//# sourceMappingURL=messageinputcontentchunks.js.map