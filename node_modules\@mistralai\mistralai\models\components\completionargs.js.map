{"version": 3, "file": "completionargs.js", "sourceRoot": "", "sources": ["../../src/models/components/completionargs.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;AAkIH,oDAEC;AAED,wDAQC;AA5ID,uCAAyB;AACzB,2DAA0D;AAC1D,qDAAiD;AAGjD,mEAKiC;AACjC,mDAKyB;AACzB,2DAK6B;AAC7B,2DAI6B;AAkB7B,gBAAgB;AACH,QAAA,4BAA4B,GAIrC,CAAC,CAAC,MAAM,CAAC;IACX,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,wDAAgC,CAAC,CAAC,QAAQ,EAAE;IAC7D,gBAAgB,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IACnD,iBAAiB,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IACpD,WAAW,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC;IACpC,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IACxC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IACnD,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IACpD,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,wCAAwB,CAAC,CAAC,QAAQ,EAAE;IAC3D,eAAe,EAAE,CAAC,CAAC,QAAQ,CAAC,gDAA4B,CAAC,CAAC,QAAQ,EAAE;IACpE,WAAW,EAAE,gDAA4B,CAAC,QAAQ,EAAE;CACrD,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,kBAAkB,EAAE,iBAAiB;QACrC,mBAAmB,EAAE,kBAAkB;QACvC,OAAO,EAAE,MAAM;QACf,YAAY,EAAE,WAAW;QACzB,aAAa,EAAE,YAAY;QAC3B,iBAAiB,EAAE,gBAAgB;QACnC,aAAa,EAAE,YAAY;KAC5B,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAgBH,gBAAgB;AACH,QAAA,6BAA6B,GAItC,CAAC,CAAC,MAAM,CAAC;IACX,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,yDAAiC,CAAC,CAAC,QAAQ,EAAE;IAC9D,eAAe,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAClD,gBAAgB,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IACnD,WAAW,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC;IACpC,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IACvC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IAClD,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IACnD,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,yCAAyB,CAAC,CAAC,QAAQ,EAAE;IAC5D,cAAc,EAAE,CAAC,CAAC,QAAQ,CAAC,iDAA6B,CAAC,CAAC,QAAQ,EAAE;IACpE,UAAU,EAAE,iDAA6B,CAAC,QAAQ,EAAE;CACrD,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,eAAe,EAAE,kBAAkB;QACnC,gBAAgB,EAAE,mBAAmB;QACrC,IAAI,EAAE,OAAO;QACb,SAAS,EAAE,YAAY;QACvB,UAAU,EAAE,aAAa;QACzB,cAAc,EAAE,iBAAiB;QACjC,UAAU,EAAE,aAAa;KAC1B,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,eAAe,CAO/B;AAPD,WAAiB,eAAe;IAC9B,8DAA8D;IACjD,6BAAa,GAAG,oCAA4B,CAAC;IAC1D,+DAA+D;IAClD,8BAAc,GAAG,qCAA6B,CAAC;AAG9D,CAAC,EAPgB,eAAe,+BAAf,eAAe,QAO/B;AAED,SAAgB,oBAAoB,CAAC,cAA8B;IACjE,OAAO,IAAI,CAAC,SAAS,CAAC,qCAA6B,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC;AAC7E,CAAC;AAED,SAAgB,sBAAsB,CACpC,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,oCAA4B,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACxD,4CAA4C,CAC7C,CAAC;AACJ,CAAC"}