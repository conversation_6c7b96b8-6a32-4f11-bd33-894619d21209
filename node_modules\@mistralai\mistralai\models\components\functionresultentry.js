"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FunctionResultEntry$ = exports.FunctionResultEntry$outboundSchema = exports.FunctionResultEntry$inboundSchema = exports.FunctionResultEntryType$ = exports.FunctionResultEntryType$outboundSchema = exports.FunctionResultEntryType$inboundSchema = exports.FunctionResultEntryObject$ = exports.FunctionResultEntryObject$outboundSchema = exports.FunctionResultEntryObject$inboundSchema = exports.FunctionResultEntryType = exports.FunctionResultEntryObject = void 0;
exports.functionResultEntryToJSON = functionResultEntryToJSON;
exports.functionResultEntryFromJSON = functionResultEntryFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
exports.FunctionResultEntryObject = {
    Entry: "entry",
};
exports.FunctionResultEntryType = {
    FunctionResult: "function.result",
};
/** @internal */
exports.FunctionResultEntryObject$inboundSchema = z.nativeEnum(exports.FunctionResultEntryObject);
/** @internal */
exports.FunctionResultEntryObject$outboundSchema = exports.FunctionResultEntryObject$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FunctionResultEntryObject$;
(function (FunctionResultEntryObject$) {
    /** @deprecated use `FunctionResultEntryObject$inboundSchema` instead. */
    FunctionResultEntryObject$.inboundSchema = exports.FunctionResultEntryObject$inboundSchema;
    /** @deprecated use `FunctionResultEntryObject$outboundSchema` instead. */
    FunctionResultEntryObject$.outboundSchema = exports.FunctionResultEntryObject$outboundSchema;
})(FunctionResultEntryObject$ || (exports.FunctionResultEntryObject$ = FunctionResultEntryObject$ = {}));
/** @internal */
exports.FunctionResultEntryType$inboundSchema = z.nativeEnum(exports.FunctionResultEntryType);
/** @internal */
exports.FunctionResultEntryType$outboundSchema = exports.FunctionResultEntryType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FunctionResultEntryType$;
(function (FunctionResultEntryType$) {
    /** @deprecated use `FunctionResultEntryType$inboundSchema` instead. */
    FunctionResultEntryType$.inboundSchema = exports.FunctionResultEntryType$inboundSchema;
    /** @deprecated use `FunctionResultEntryType$outboundSchema` instead. */
    FunctionResultEntryType$.outboundSchema = exports.FunctionResultEntryType$outboundSchema;
})(FunctionResultEntryType$ || (exports.FunctionResultEntryType$ = FunctionResultEntryType$ = {}));
/** @internal */
exports.FunctionResultEntry$inboundSchema = z.object({
    object: exports.FunctionResultEntryObject$inboundSchema.default("entry"),
    type: exports.FunctionResultEntryType$inboundSchema.default("function.result"),
    created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
        .optional(),
    completed_at: z.nullable(z.string().datetime({ offset: true }).transform(v => new Date(v))).optional(),
    id: z.string().optional(),
    tool_call_id: z.string(),
    result: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "created_at": "createdAt",
        "completed_at": "completedAt",
        "tool_call_id": "toolCallId",
    });
});
/** @internal */
exports.FunctionResultEntry$outboundSchema = z.object({
    object: exports.FunctionResultEntryObject$outboundSchema.default("entry"),
    type: exports.FunctionResultEntryType$outboundSchema.default("function.result"),
    createdAt: z.date().transform(v => v.toISOString()).optional(),
    completedAt: z.nullable(z.date().transform(v => v.toISOString())).optional(),
    id: z.string().optional(),
    toolCallId: z.string(),
    result: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        createdAt: "created_at",
        completedAt: "completed_at",
        toolCallId: "tool_call_id",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FunctionResultEntry$;
(function (FunctionResultEntry$) {
    /** @deprecated use `FunctionResultEntry$inboundSchema` instead. */
    FunctionResultEntry$.inboundSchema = exports.FunctionResultEntry$inboundSchema;
    /** @deprecated use `FunctionResultEntry$outboundSchema` instead. */
    FunctionResultEntry$.outboundSchema = exports.FunctionResultEntry$outboundSchema;
})(FunctionResultEntry$ || (exports.FunctionResultEntry$ = FunctionResultEntry$ = {}));
function functionResultEntryToJSON(functionResultEntry) {
    return JSON.stringify(exports.FunctionResultEntry$outboundSchema.parse(functionResultEntry));
}
function functionResultEntryFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.FunctionResultEntry$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'FunctionResultEntry' from JSON`);
}
//# sourceMappingURL=functionresultentry.js.map