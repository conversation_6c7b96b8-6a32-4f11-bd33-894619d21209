"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSearchTool$ = exports.WebSearchTool$outboundSchema = exports.WebSearchTool$inboundSchema = exports.WebSearchToolType$ = exports.WebSearchToolType$outboundSchema = exports.WebSearchToolType$inboundSchema = exports.WebSearchToolType = void 0;
exports.webSearchToolToJSON = webSearchToolToJSON;
exports.webSearchToolFromJSON = webSearchToolFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
exports.WebSearchToolType = {
    WebSearch: "web_search",
};
/** @internal */
exports.WebSearchToolType$inboundSchema = z.nativeEnum(exports.WebSearchToolType);
/** @internal */
exports.WebSearchToolType$outboundSchema = exports.WebSearchToolType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebSearchToolType$;
(function (WebSearchToolType$) {
    /** @deprecated use `WebSearchToolType$inboundSchema` instead. */
    WebSearchToolType$.inboundSchema = exports.WebSearchToolType$inboundSchema;
    /** @deprecated use `WebSearchToolType$outboundSchema` instead. */
    WebSearchToolType$.outboundSchema = exports.WebSearchToolType$outboundSchema;
})(WebSearchToolType$ || (exports.WebSearchToolType$ = WebSearchToolType$ = {}));
/** @internal */
exports.WebSearchTool$inboundSchema = z.object({
    type: exports.WebSearchToolType$inboundSchema.default("web_search"),
});
/** @internal */
exports.WebSearchTool$outboundSchema = z.object({
    type: exports.WebSearchToolType$outboundSchema.default("web_search"),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebSearchTool$;
(function (WebSearchTool$) {
    /** @deprecated use `WebSearchTool$inboundSchema` instead. */
    WebSearchTool$.inboundSchema = exports.WebSearchTool$inboundSchema;
    /** @deprecated use `WebSearchTool$outboundSchema` instead. */
    WebSearchTool$.outboundSchema = exports.WebSearchTool$outboundSchema;
})(WebSearchTool$ || (exports.WebSearchTool$ = WebSearchTool$ = {}));
function webSearchToolToJSON(webSearchTool) {
    return JSON.stringify(exports.WebSearchTool$outboundSchema.parse(webSearchTool));
}
function webSearchToolFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.WebSearchTool$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'WebSearchTool' from JSON`);
}
//# sourceMappingURL=websearchtool.js.map