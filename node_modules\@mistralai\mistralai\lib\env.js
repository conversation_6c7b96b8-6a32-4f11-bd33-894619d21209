"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.envSchema = void 0;
exports.env = env;
exports.resetEnv = resetEnv;
const dlv_js_1 = require("./dlv.js");
const z = __importStar(require("zod"));
exports.envSchema = z.object({
    MISTRAL_API_KEY: z.string().optional(),
    MISTRAL_DEBUG: z.coerce.boolean().optional(),
});
let envMemo = undefined;
/**
 * Reads and validates environment variables.
 */
function env() {
    if (envMemo) {
        return envMemo;
    }
    envMemo = exports.envSchema.parse((0, dlv_js_1.dlv)(globalThis, "process.env") ?? (0, dlv_js_1.dlv)(globalThis, "Deno.env") ?? {});
    return envMemo;
}
/**
 * Clears the cached env object. Useful for testing with a fresh environment.
 */
function resetEnv() {
    envMemo = undefined;
}
//# sourceMappingURL=env.js.map