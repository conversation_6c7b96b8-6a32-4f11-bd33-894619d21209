"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Tool$ = exports.Tool$outboundSchema = exports.Tool$inboundSchema = void 0;
exports.toolToJSON = toolToJSON;
exports.toolFromJSON = toolFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const function_js_1 = require("./function.js");
const tooltypes_js_1 = require("./tooltypes.js");
/** @internal */
exports.Tool$inboundSchema = z
    .object({
    type: tooltypes_js_1.ToolTypes$inboundSchema.optional(),
    function: function_js_1.FunctionT$inboundSchema,
});
/** @internal */
exports.Tool$outboundSchema = z.object({
    type: tooltypes_js_1.ToolTypes$outboundSchema.optional(),
    function: function_js_1.FunctionT$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Tool$;
(function (Tool$) {
    /** @deprecated use `Tool$inboundSchema` instead. */
    Tool$.inboundSchema = exports.Tool$inboundSchema;
    /** @deprecated use `Tool$outboundSchema` instead. */
    Tool$.outboundSchema = exports.Tool$outboundSchema;
})(Tool$ || (exports.Tool$ = Tool$ = {}));
function toolToJSON(tool) {
    return JSON.stringify(exports.Tool$outboundSchema.parse(tool));
}
function toolFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.Tool$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'Tool' from JSON`);
}
//# sourceMappingURL=tool.js.map