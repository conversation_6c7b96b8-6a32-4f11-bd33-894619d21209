"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FunctionCallEvent$ = exports.FunctionCallEvent$outboundSchema = exports.FunctionCallEvent$inboundSchema = exports.FunctionCallEventType$ = exports.FunctionCallEventType$outboundSchema = exports.FunctionCallEventType$inboundSchema = exports.FunctionCallEventType = void 0;
exports.functionCallEventToJSON = functionCallEventToJSON;
exports.functionCallEventFromJSON = functionCallEventFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
exports.FunctionCallEventType = {
    FunctionCallDelta: "function.call.delta",
};
/** @internal */
exports.FunctionCallEventType$inboundSchema = z.nativeEnum(exports.FunctionCallEventType);
/** @internal */
exports.FunctionCallEventType$outboundSchema = exports.FunctionCallEventType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FunctionCallEventType$;
(function (FunctionCallEventType$) {
    /** @deprecated use `FunctionCallEventType$inboundSchema` instead. */
    FunctionCallEventType$.inboundSchema = exports.FunctionCallEventType$inboundSchema;
    /** @deprecated use `FunctionCallEventType$outboundSchema` instead. */
    FunctionCallEventType$.outboundSchema = exports.FunctionCallEventType$outboundSchema;
})(FunctionCallEventType$ || (exports.FunctionCallEventType$ = FunctionCallEventType$ = {}));
/** @internal */
exports.FunctionCallEvent$inboundSchema = z.object({
    type: exports.FunctionCallEventType$inboundSchema.default("function.call.delta"),
    created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
        .optional(),
    output_index: z.number().int().default(0),
    id: z.string(),
    name: z.string(),
    tool_call_id: z.string(),
    arguments: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "created_at": "createdAt",
        "output_index": "outputIndex",
        "tool_call_id": "toolCallId",
    });
});
/** @internal */
exports.FunctionCallEvent$outboundSchema = z.object({
    type: exports.FunctionCallEventType$outboundSchema.default("function.call.delta"),
    createdAt: z.date().transform(v => v.toISOString()).optional(),
    outputIndex: z.number().int().default(0),
    id: z.string(),
    name: z.string(),
    toolCallId: z.string(),
    arguments: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        createdAt: "created_at",
        outputIndex: "output_index",
        toolCallId: "tool_call_id",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FunctionCallEvent$;
(function (FunctionCallEvent$) {
    /** @deprecated use `FunctionCallEvent$inboundSchema` instead. */
    FunctionCallEvent$.inboundSchema = exports.FunctionCallEvent$inboundSchema;
    /** @deprecated use `FunctionCallEvent$outboundSchema` instead. */
    FunctionCallEvent$.outboundSchema = exports.FunctionCallEvent$outboundSchema;
})(FunctionCallEvent$ || (exports.FunctionCallEvent$ = FunctionCallEvent$ = {}));
function functionCallEventToJSON(functionCallEvent) {
    return JSON.stringify(exports.FunctionCallEvent$outboundSchema.parse(functionCallEvent));
}
function functionCallEventFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.FunctionCallEvent$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'FunctionCallEvent' from JSON`);
}
//# sourceMappingURL=functioncallevent.js.map