"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Inputs$ = exports.Inputs$outboundSchema = exports.Inputs$inboundSchema = exports.InstructRequestInputs$ = exports.InstructRequestInputs$outboundSchema = exports.InstructRequestInputs$inboundSchema = exports.InstructRequestInputsMessages$ = exports.InstructRequestInputsMessages$outboundSchema = exports.InstructRequestInputsMessages$inboundSchema = void 0;
exports.instructRequestInputsMessagesToJSON = instructRequestInputsMessagesToJSON;
exports.instructRequestInputsMessagesFromJSON = instructRequestInputsMessagesFromJSON;
exports.instructRequestInputsToJSON = instructRequestInputsToJSON;
exports.instructRequestInputsFromJSON = instructRequestInputsFromJSON;
exports.inputsToJSON = inputsToJSON;
exports.inputsFromJSON = inputsFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const assistantmessage_js_1 = require("./assistantmessage.js");
const instructrequest_js_1 = require("./instructrequest.js");
const systemmessage_js_1 = require("./systemmessage.js");
const toolmessage_js_1 = require("./toolmessage.js");
const usermessage_js_1 = require("./usermessage.js");
/** @internal */
exports.InstructRequestInputsMessages$inboundSchema = z.union([
    systemmessage_js_1.SystemMessage$inboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
        role: v.role,
    }))),
    usermessage_js_1.UserMessage$inboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({ role: v.role }))),
    assistantmessage_js_1.AssistantMessage$inboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
        role: v.role,
    }))),
    toolmessage_js_1.ToolMessage$inboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({ role: v.role }))),
]);
/** @internal */
exports.InstructRequestInputsMessages$outboundSchema = z.union([
    systemmessage_js_1.SystemMessage$outboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
        role: v.role,
    }))),
    usermessage_js_1.UserMessage$outboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({ role: v.role }))),
    assistantmessage_js_1.AssistantMessage$outboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
        role: v.role,
    }))),
    toolmessage_js_1.ToolMessage$outboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({ role: v.role }))),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var InstructRequestInputsMessages$;
(function (InstructRequestInputsMessages$) {
    /** @deprecated use `InstructRequestInputsMessages$inboundSchema` instead. */
    InstructRequestInputsMessages$.inboundSchema = exports.InstructRequestInputsMessages$inboundSchema;
    /** @deprecated use `InstructRequestInputsMessages$outboundSchema` instead. */
    InstructRequestInputsMessages$.outboundSchema = exports.InstructRequestInputsMessages$outboundSchema;
})(InstructRequestInputsMessages$ || (exports.InstructRequestInputsMessages$ = InstructRequestInputsMessages$ = {}));
function instructRequestInputsMessagesToJSON(instructRequestInputsMessages) {
    return JSON.stringify(exports.InstructRequestInputsMessages$outboundSchema.parse(instructRequestInputsMessages));
}
function instructRequestInputsMessagesFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.InstructRequestInputsMessages$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'InstructRequestInputsMessages' from JSON`);
}
/** @internal */
exports.InstructRequestInputs$inboundSchema = z.object({
    messages: z.array(z.union([
        systemmessage_js_1.SystemMessage$inboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
            role: v.role,
        }))),
        usermessage_js_1.UserMessage$inboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({
            role: v.role,
        }))),
        assistantmessage_js_1.AssistantMessage$inboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
            role: v.role,
        }))),
        toolmessage_js_1.ToolMessage$inboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({
            role: v.role,
        }))),
    ])),
});
/** @internal */
exports.InstructRequestInputs$outboundSchema = z.object({
    messages: z.array(z.union([
        systemmessage_js_1.SystemMessage$outboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
            role: v.role,
        }))),
        usermessage_js_1.UserMessage$outboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({
            role: v.role,
        }))),
        assistantmessage_js_1.AssistantMessage$outboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
            role: v.role,
        }))),
        toolmessage_js_1.ToolMessage$outboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({
            role: v.role,
        }))),
    ])),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var InstructRequestInputs$;
(function (InstructRequestInputs$) {
    /** @deprecated use `InstructRequestInputs$inboundSchema` instead. */
    InstructRequestInputs$.inboundSchema = exports.InstructRequestInputs$inboundSchema;
    /** @deprecated use `InstructRequestInputs$outboundSchema` instead. */
    InstructRequestInputs$.outboundSchema = exports.InstructRequestInputs$outboundSchema;
})(InstructRequestInputs$ || (exports.InstructRequestInputs$ = InstructRequestInputs$ = {}));
function instructRequestInputsToJSON(instructRequestInputs) {
    return JSON.stringify(exports.InstructRequestInputs$outboundSchema.parse(instructRequestInputs));
}
function instructRequestInputsFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.InstructRequestInputs$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'InstructRequestInputs' from JSON`);
}
/** @internal */
exports.Inputs$inboundSchema = z
    .union([
    z.lazy(() => exports.InstructRequestInputs$inboundSchema),
    z.array(instructrequest_js_1.InstructRequest$inboundSchema),
]);
/** @internal */
exports.Inputs$outboundSchema = z.union([
    z.lazy(() => exports.InstructRequestInputs$outboundSchema),
    z.array(instructrequest_js_1.InstructRequest$outboundSchema),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Inputs$;
(function (Inputs$) {
    /** @deprecated use `Inputs$inboundSchema` instead. */
    Inputs$.inboundSchema = exports.Inputs$inboundSchema;
    /** @deprecated use `Inputs$outboundSchema` instead. */
    Inputs$.outboundSchema = exports.Inputs$outboundSchema;
})(Inputs$ || (exports.Inputs$ = Inputs$ = {}));
function inputsToJSON(inputs) {
    return JSON.stringify(exports.Inputs$outboundSchema.parse(inputs));
}
function inputsFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.Inputs$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'Inputs' from JSON`);
}
//# sourceMappingURL=inputs.js.map