"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BuiltInConnectors$ = exports.BuiltInConnectors$outboundSchema = exports.BuiltInConnectors$inboundSchema = exports.BuiltInConnectors = void 0;
const z = __importStar(require("zod"));
exports.BuiltInConnectors = {
    WebSearch: "web_search",
    WebSearchPremium: "web_search_premium",
    CodeInterpreter: "code_interpreter",
    ImageGeneration: "image_generation",
    DocumentLibrary: "document_library",
};
/** @internal */
exports.BuiltInConnectors$inboundSchema = z.nativeEnum(exports.BuiltInConnectors);
/** @internal */
exports.BuiltInConnectors$outboundSchema = exports.BuiltInConnectors$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var BuiltInConnectors$;
(function (BuiltInConnectors$) {
    /** @deprecated use `BuiltInConnectors$inboundSchema` instead. */
    BuiltInConnectors$.inboundSchema = exports.BuiltInConnectors$inboundSchema;
    /** @deprecated use `BuiltInConnectors$outboundSchema` instead. */
    BuiltInConnectors$.outboundSchema = exports.BuiltInConnectors$outboundSchema;
})(BuiltInConnectors$ || (exports.BuiltInConnectors$ = BuiltInConnectors$ = {}));
//# sourceMappingURL=builtinconnectors.js.map