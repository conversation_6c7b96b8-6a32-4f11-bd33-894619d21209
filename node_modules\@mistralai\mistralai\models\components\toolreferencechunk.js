"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolReferenceChunk$ = exports.ToolReferenceChunk$outboundSchema = exports.ToolReferenceChunk$inboundSchema = exports.ToolReferenceChunkType$ = exports.ToolReferenceChunkType$outboundSchema = exports.ToolReferenceChunkType$inboundSchema = exports.ToolReferenceChunkType = void 0;
exports.toolReferenceChunkToJSON = toolReferenceChunkToJSON;
exports.toolReferenceChunkFromJSON = toolReferenceChunkFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const builtinconnectors_js_1 = require("./builtinconnectors.js");
exports.ToolReferenceChunkType = {
    ToolReference: "tool_reference",
};
/** @internal */
exports.ToolReferenceChunkType$inboundSchema = z.nativeEnum(exports.ToolReferenceChunkType);
/** @internal */
exports.ToolReferenceChunkType$outboundSchema = exports.ToolReferenceChunkType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ToolReferenceChunkType$;
(function (ToolReferenceChunkType$) {
    /** @deprecated use `ToolReferenceChunkType$inboundSchema` instead. */
    ToolReferenceChunkType$.inboundSchema = exports.ToolReferenceChunkType$inboundSchema;
    /** @deprecated use `ToolReferenceChunkType$outboundSchema` instead. */
    ToolReferenceChunkType$.outboundSchema = exports.ToolReferenceChunkType$outboundSchema;
})(ToolReferenceChunkType$ || (exports.ToolReferenceChunkType$ = ToolReferenceChunkType$ = {}));
/** @internal */
exports.ToolReferenceChunk$inboundSchema = z.object({
    type: exports.ToolReferenceChunkType$inboundSchema.default("tool_reference"),
    tool: builtinconnectors_js_1.BuiltInConnectors$inboundSchema,
    title: z.string(),
    url: z.nullable(z.string()).optional(),
    source: z.nullable(z.string()).optional(),
});
/** @internal */
exports.ToolReferenceChunk$outboundSchema = z.object({
    type: exports.ToolReferenceChunkType$outboundSchema.default("tool_reference"),
    tool: builtinconnectors_js_1.BuiltInConnectors$outboundSchema,
    title: z.string(),
    url: z.nullable(z.string()).optional(),
    source: z.nullable(z.string()).optional(),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ToolReferenceChunk$;
(function (ToolReferenceChunk$) {
    /** @deprecated use `ToolReferenceChunk$inboundSchema` instead. */
    ToolReferenceChunk$.inboundSchema = exports.ToolReferenceChunk$inboundSchema;
    /** @deprecated use `ToolReferenceChunk$outboundSchema` instead. */
    ToolReferenceChunk$.outboundSchema = exports.ToolReferenceChunk$outboundSchema;
})(ToolReferenceChunk$ || (exports.ToolReferenceChunk$ = ToolReferenceChunk$ = {}));
function toolReferenceChunkToJSON(toolReferenceChunk) {
    return JSON.stringify(exports.ToolReferenceChunk$outboundSchema.parse(toolReferenceChunk));
}
function toolReferenceChunkFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ToolReferenceChunk$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ToolReferenceChunk' from JSON`);
}
//# sourceMappingURL=toolreferencechunk.js.map