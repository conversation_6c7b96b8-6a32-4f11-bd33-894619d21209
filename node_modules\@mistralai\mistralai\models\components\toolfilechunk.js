"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolFileChunk$ = exports.ToolFileChunk$outboundSchema = exports.ToolFileChunk$inboundSchema = exports.ToolFileChunkType$ = exports.ToolFileChunkType$outboundSchema = exports.ToolFileChunkType$inboundSchema = exports.ToolFileChunkType = void 0;
exports.toolFileChunkToJSON = toolFileChunkToJSON;
exports.toolFileChunkFromJSON = toolFileChunkFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const builtinconnectors_js_1 = require("./builtinconnectors.js");
exports.ToolFileChunkType = {
    ToolFile: "tool_file",
};
/** @internal */
exports.ToolFileChunkType$inboundSchema = z.nativeEnum(exports.ToolFileChunkType);
/** @internal */
exports.ToolFileChunkType$outboundSchema = exports.ToolFileChunkType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ToolFileChunkType$;
(function (ToolFileChunkType$) {
    /** @deprecated use `ToolFileChunkType$inboundSchema` instead. */
    ToolFileChunkType$.inboundSchema = exports.ToolFileChunkType$inboundSchema;
    /** @deprecated use `ToolFileChunkType$outboundSchema` instead. */
    ToolFileChunkType$.outboundSchema = exports.ToolFileChunkType$outboundSchema;
})(ToolFileChunkType$ || (exports.ToolFileChunkType$ = ToolFileChunkType$ = {}));
/** @internal */
exports.ToolFileChunk$inboundSchema = z.object({
    type: exports.ToolFileChunkType$inboundSchema.default("tool_file"),
    tool: builtinconnectors_js_1.BuiltInConnectors$inboundSchema,
    file_id: z.string(),
    file_name: z.nullable(z.string()).optional(),
    file_type: z.nullable(z.string()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "file_id": "fileId",
        "file_name": "fileName",
        "file_type": "fileType",
    });
});
/** @internal */
exports.ToolFileChunk$outboundSchema = z.object({
    type: exports.ToolFileChunkType$outboundSchema.default("tool_file"),
    tool: builtinconnectors_js_1.BuiltInConnectors$outboundSchema,
    fileId: z.string(),
    fileName: z.nullable(z.string()).optional(),
    fileType: z.nullable(z.string()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        fileId: "file_id",
        fileName: "file_name",
        fileType: "file_type",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ToolFileChunk$;
(function (ToolFileChunk$) {
    /** @deprecated use `ToolFileChunk$inboundSchema` instead. */
    ToolFileChunk$.inboundSchema = exports.ToolFileChunk$inboundSchema;
    /** @deprecated use `ToolFileChunk$outboundSchema` instead. */
    ToolFileChunk$.outboundSchema = exports.ToolFileChunk$outboundSchema;
})(ToolFileChunk$ || (exports.ToolFileChunk$ = ToolFileChunk$ = {}));
function toolFileChunkToJSON(toolFileChunk) {
    return JSON.stringify(exports.ToolFileChunk$outboundSchema.parse(toolFileChunk));
}
function toolFileChunkFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ToolFileChunk$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ToolFileChunk' from JSON`);
}
//# sourceMappingURL=toolfilechunk.js.map