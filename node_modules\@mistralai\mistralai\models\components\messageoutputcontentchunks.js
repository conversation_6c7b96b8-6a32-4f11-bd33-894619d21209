"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageOutputContentChunks$ = exports.MessageOutputContentChunks$outboundSchema = exports.MessageOutputContentChunks$inboundSchema = void 0;
exports.messageOutputContentChunksToJSON = messageOutputContentChunksToJSON;
exports.messageOutputContentChunksFromJSON = messageOutputContentChunksFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const documenturlchunk_js_1 = require("./documenturlchunk.js");
const imageurlchunk_js_1 = require("./imageurlchunk.js");
const textchunk_js_1 = require("./textchunk.js");
const toolfilechunk_js_1 = require("./toolfilechunk.js");
const toolreferencechunk_js_1 = require("./toolreferencechunk.js");
/** @internal */
exports.MessageOutputContentChunks$inboundSchema = z.union([
    textchunk_js_1.TextChunk$inboundSchema,
    imageurlchunk_js_1.ImageURLChunk$inboundSchema,
    documenturlchunk_js_1.DocumentURLChunk$inboundSchema,
    toolfilechunk_js_1.ToolFileChunk$inboundSchema,
    toolreferencechunk_js_1.ToolReferenceChunk$inboundSchema,
]);
/** @internal */
exports.MessageOutputContentChunks$outboundSchema = z.union([
    textchunk_js_1.TextChunk$outboundSchema,
    imageurlchunk_js_1.ImageURLChunk$outboundSchema,
    documenturlchunk_js_1.DocumentURLChunk$outboundSchema,
    toolfilechunk_js_1.ToolFileChunk$outboundSchema,
    toolreferencechunk_js_1.ToolReferenceChunk$outboundSchema,
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var MessageOutputContentChunks$;
(function (MessageOutputContentChunks$) {
    /** @deprecated use `MessageOutputContentChunks$inboundSchema` instead. */
    MessageOutputContentChunks$.inboundSchema = exports.MessageOutputContentChunks$inboundSchema;
    /** @deprecated use `MessageOutputContentChunks$outboundSchema` instead. */
    MessageOutputContentChunks$.outboundSchema = exports.MessageOutputContentChunks$outboundSchema;
})(MessageOutputContentChunks$ || (exports.MessageOutputContentChunks$ = MessageOutputContentChunks$ = {}));
function messageOutputContentChunksToJSON(messageOutputContentChunks) {
    return JSON.stringify(exports.MessageOutputContentChunks$outboundSchema.parse(messageOutputContentChunks));
}
function messageOutputContentChunksFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.MessageOutputContentChunks$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'MessageOutputContentChunks' from JSON`);
}
//# sourceMappingURL=messageoutputcontentchunks.js.map