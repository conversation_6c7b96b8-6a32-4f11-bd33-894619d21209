"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentLibraryTool$ = exports.DocumentLibraryTool$outboundSchema = exports.DocumentLibraryTool$inboundSchema = exports.DocumentLibraryToolType$ = exports.DocumentLibraryToolType$outboundSchema = exports.DocumentLibraryToolType$inboundSchema = exports.DocumentLibraryToolType = void 0;
exports.documentLibraryToolToJSON = documentLibraryToolToJSON;
exports.documentLibraryToolFromJSON = documentLibraryToolFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
exports.DocumentLibraryToolType = {
    DocumentLibrary: "document_library",
};
/** @internal */
exports.DocumentLibraryToolType$inboundSchema = z.nativeEnum(exports.DocumentLibraryToolType);
/** @internal */
exports.DocumentLibraryToolType$outboundSchema = exports.DocumentLibraryToolType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var DocumentLibraryToolType$;
(function (DocumentLibraryToolType$) {
    /** @deprecated use `DocumentLibraryToolType$inboundSchema` instead. */
    DocumentLibraryToolType$.inboundSchema = exports.DocumentLibraryToolType$inboundSchema;
    /** @deprecated use `DocumentLibraryToolType$outboundSchema` instead. */
    DocumentLibraryToolType$.outboundSchema = exports.DocumentLibraryToolType$outboundSchema;
})(DocumentLibraryToolType$ || (exports.DocumentLibraryToolType$ = DocumentLibraryToolType$ = {}));
/** @internal */
exports.DocumentLibraryTool$inboundSchema = z.object({
    type: exports.DocumentLibraryToolType$inboundSchema.default("document_library"),
    library_ids: z.array(z.string()),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "library_ids": "libraryIds",
    });
});
/** @internal */
exports.DocumentLibraryTool$outboundSchema = z.object({
    type: exports.DocumentLibraryToolType$outboundSchema.default("document_library"),
    libraryIds: z.array(z.string()),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        libraryIds: "library_ids",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var DocumentLibraryTool$;
(function (DocumentLibraryTool$) {
    /** @deprecated use `DocumentLibraryTool$inboundSchema` instead. */
    DocumentLibraryTool$.inboundSchema = exports.DocumentLibraryTool$inboundSchema;
    /** @deprecated use `DocumentLibraryTool$outboundSchema` instead. */
    DocumentLibraryTool$.outboundSchema = exports.DocumentLibraryTool$outboundSchema;
})(DocumentLibraryTool$ || (exports.DocumentLibraryTool$ = DocumentLibraryTool$ = {}));
function documentLibraryToolToJSON(documentLibraryTool) {
    return JSON.stringify(exports.DocumentLibraryTool$outboundSchema.parse(documentLibraryTool));
}
function documentLibraryToolFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.DocumentLibraryTool$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'DocumentLibraryTool' from JSON`);
}
//# sourceMappingURL=documentlibrarytool.js.map