"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentHandoffDoneEvent$ = exports.AgentHandoffDoneEvent$outboundSchema = exports.AgentHandoffDoneEvent$inboundSchema = exports.AgentHandoffDoneEventType$ = exports.AgentHandoffDoneEventType$outboundSchema = exports.AgentHandoffDoneEventType$inboundSchema = exports.AgentHandoffDoneEventType = void 0;
exports.agentHandoffDoneEventToJSON = agentHandoffDoneEventToJSON;
exports.agentHandoffDoneEventFromJSON = agentHandoffDoneEventFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
exports.AgentHandoffDoneEventType = {
    AgentHandoffDone: "agent.handoff.done",
};
/** @internal */
exports.AgentHandoffDoneEventType$inboundSchema = z.nativeEnum(exports.AgentHandoffDoneEventType);
/** @internal */
exports.AgentHandoffDoneEventType$outboundSchema = exports.AgentHandoffDoneEventType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentHandoffDoneEventType$;
(function (AgentHandoffDoneEventType$) {
    /** @deprecated use `AgentHandoffDoneEventType$inboundSchema` instead. */
    AgentHandoffDoneEventType$.inboundSchema = exports.AgentHandoffDoneEventType$inboundSchema;
    /** @deprecated use `AgentHandoffDoneEventType$outboundSchema` instead. */
    AgentHandoffDoneEventType$.outboundSchema = exports.AgentHandoffDoneEventType$outboundSchema;
})(AgentHandoffDoneEventType$ || (exports.AgentHandoffDoneEventType$ = AgentHandoffDoneEventType$ = {}));
/** @internal */
exports.AgentHandoffDoneEvent$inboundSchema = z.object({
    type: exports.AgentHandoffDoneEventType$inboundSchema.default("agent.handoff.done"),
    created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
        .optional(),
    output_index: z.number().int().default(0),
    id: z.string(),
    next_agent_id: z.string(),
    next_agent_name: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "created_at": "createdAt",
        "output_index": "outputIndex",
        "next_agent_id": "nextAgentId",
        "next_agent_name": "nextAgentName",
    });
});
/** @internal */
exports.AgentHandoffDoneEvent$outboundSchema = z.object({
    type: exports.AgentHandoffDoneEventType$outboundSchema.default("agent.handoff.done"),
    createdAt: z.date().transform(v => v.toISOString()).optional(),
    outputIndex: z.number().int().default(0),
    id: z.string(),
    nextAgentId: z.string(),
    nextAgentName: z.string(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        createdAt: "created_at",
        outputIndex: "output_index",
        nextAgentId: "next_agent_id",
        nextAgentName: "next_agent_name",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AgentHandoffDoneEvent$;
(function (AgentHandoffDoneEvent$) {
    /** @deprecated use `AgentHandoffDoneEvent$inboundSchema` instead. */
    AgentHandoffDoneEvent$.inboundSchema = exports.AgentHandoffDoneEvent$inboundSchema;
    /** @deprecated use `AgentHandoffDoneEvent$outboundSchema` instead. */
    AgentHandoffDoneEvent$.outboundSchema = exports.AgentHandoffDoneEvent$outboundSchema;
})(AgentHandoffDoneEvent$ || (exports.AgentHandoffDoneEvent$ = AgentHandoffDoneEvent$ = {}));
function agentHandoffDoneEventToJSON(agentHandoffDoneEvent) {
    return JSON.stringify(exports.AgentHandoffDoneEvent$outboundSchema.parse(agentHandoffDoneEvent));
}
function agentHandoffDoneEventFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.AgentHandoffDoneEvent$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'AgentHandoffDoneEvent' from JSON`);
}
//# sourceMappingURL=agenthandoffdoneevent.js.map