{"version": 3, "file": "conversationstreamrequest.js", "sourceRoot": "", "sources": ["../../src/models/components/conversationstreamrequest.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;AA6NH,oFAQC;AAED,wFAQC;AAoKD,0EAMC;AAED,8EAQC;AAjaD,uCAAyB;AACzB,2DAA0D;AAC1D,qDAAiD;AAIjD,qEAKkC;AAClC,2DAK6B;AAC7B,mEAKiC;AACjC,qEAKkC;AAClC,uDAK2B;AAC3B,qEAKkC;AAClC,uEAKmC;AACnC,yDAK4B;AAEf,QAAA,yCAAyC,GAAG;IACvD,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,QAAQ;CACR,CAAC;AAwCX,gBAAgB;AACH,QAAA,uDAAuD,GACE,CAAC;KAClE,UAAU,CAAC,iDAAyC,CAAC,CAAC;AAE3D,gBAAgB;AACH,QAAA,wDAAwD,GAEjE,+DAAuD,CAAC;AAE5D;;;GAGG;AACH,IAAiB,0CAA0C,CAO1D;AAPD,WAAiB,0CAA0C;IACzD,yFAAyF;IAC5E,wDAAa,GACxB,+DAAuD,CAAC;IAC1D,0FAA0F;IAC7E,yDAAc,GACzB,gEAAwD,CAAC;AAC7D,CAAC,EAPgB,0CAA0C,0DAA1C,0CAA0C,QAO1D;AAED,gBAAgB;AACH,QAAA,4CAA4C,GAIrD,CAAC,CAAC,KAAK,CAAC;IACV,0DAAiC,CAAC,GAAG,CACnC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClE,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,0DAAiC,CAAC,GAAG,CACnC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClE,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,8CAA2B,CAAC,GAAG,CAC7B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,4DAAkC,CAAC,GAAG,CACpC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACpE,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,0DAAiC,CAAC,GAAG,CACnC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClE,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,4CAA0B,CAAC,GAAG,CAC5B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC1D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;CACF,CAAC,CAAC;AAWH,gBAAgB;AACH,QAAA,6CAA6C,GAItD,CAAC,CAAC,KAAK,CAAC;IACV,2DAAkC,CAAC,GAAG,CACpC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClE,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,2DAAkC,CAAC,GAAG,CACpC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClE,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,+CAA4B,CAAC,GAAG,CAC9B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,6DAAmC,CAAC,GAAG,CACrC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACpE,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,2DAAkC,CAAC,GAAG,CACpC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClE,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,6CAA2B,CAAC,GAAG,CAC7B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC1D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;CACF,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,+BAA+B,CAO/C;AAPD,WAAiB,+BAA+B;IAC9C,8EAA8E;IACjE,6CAAa,GAAG,oDAA4C,CAAC;IAC1E,+EAA+E;IAClE,8CAAc,GAAG,qDAA6C,CAAC;AAG9E,CAAC,EAPgB,+BAA+B,+CAA/B,+BAA+B,QAO/C;AAED,SAAgB,oCAAoC,CAClD,8BAA8D;IAE9D,OAAO,IAAI,CAAC,SAAS,CACnB,qDAA6C,CAAC,KAAK,CACjD,8BAA8B,CAC/B,CACF,CAAC;AACJ,CAAC;AAED,SAAgB,sCAAsC,CACpD,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,oDAA4C,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACxE,4DAA4D,CAC7D,CAAC;AACJ,CAAC;AAED,gBAAgB;AACH,QAAA,uCAAuC,GAIhD,CAAC,CAAC,MAAM,CAAC;IACX,MAAM,EAAE,wDAAgC;IACxC,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACjC,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE;IACzC,iBAAiB,EAAE,CAAC,CAAC,QAAQ,CAC3B,+DAAuD,CACxD,CAAC,QAAQ,EAAE;IACZ,YAAY,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC/C,KAAK,EAAE,CAAC,CAAC,QAAQ,CACf,CAAC,CAAC,KAAK,CACL,CAAC,CAAC,KAAK,CAAC;QACN,0DAAiC,CAAC,GAAG,CACnC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAClE,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,0DAAiC,CAAC,GAAG,CACnC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAClE,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,8CAA2B,CAAC,GAAG,CAC7B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC5D,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,4DAAkC,CAAC,GAAG,CACpC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAC5D,CAAC,EACD,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CACzB;QACD,0DAAiC,CAAC,GAAG,CACnC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAClE,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,4CAA0B,CAAC,GAAG,CAC5B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC1D,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;KACF,CAAC,CACH,CACF,CAAC,QAAQ,EAAE;IACZ,eAAe,EAAE,CAAC,CAAC,QAAQ,CAAC,gDAA4B,CAAC,CAAC,QAAQ,EAAE;IACpE,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IACvC,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC9C,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC3C,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;CACzC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,mBAAmB,EAAE,kBAAkB;QACvC,iBAAiB,EAAE,gBAAgB;QACnC,UAAU,EAAE,SAAS;KACtB,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AA2BH,gBAAgB;AACH,QAAA,wCAAwC,GAIjD,CAAC,CAAC,MAAM,CAAC;IACX,MAAM,EAAE,yDAAiC;IACzC,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACjC,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE;IACzC,gBAAgB,EAAE,CAAC,CAAC,QAAQ,CAC1B,gEAAwD,CACzD,CAAC,QAAQ,EAAE;IACZ,YAAY,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC/C,KAAK,EAAE,CAAC,CAAC,QAAQ,CACf,CAAC,CAAC,KAAK,CACL,CAAC,CAAC,KAAK,CAAC;QACN,2DAAkC,CAAC,GAAG,CACpC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAClE,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,2DAAkC,CAAC,GAAG,CACpC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAClE,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,+CAA4B,CAAC,GAAG,CAC9B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC5D,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,6DAAmC,CAAC,GAAG,CACrC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAC5D,CAAC,EACD,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CACzB;QACD,2DAAkC,CAAC,GAAG,CACpC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAClE,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,6CAA2B,CAAC,GAAG,CAC7B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC1D,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;KACF,CAAC,CACH,CACF,CAAC,QAAQ,EAAE;IACZ,cAAc,EAAE,CAAC,CAAC,QAAQ,CAAC,iDAA6B,CAAC,CAAC,QAAQ,EAAE;IACpE,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IACvC,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC9C,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC1C,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;CACzC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,gBAAgB,EAAE,mBAAmB;QACrC,cAAc,EAAE,iBAAiB;QACjC,OAAO,EAAE,UAAU;KACpB,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,0BAA0B,CAO1C;AAPD,WAAiB,0BAA0B;IACzC,yEAAyE;IAC5D,wCAAa,GAAG,+CAAuC,CAAC;IACrE,0EAA0E;IAC7D,yCAAc,GAAG,gDAAwC,CAAC;AAGzE,CAAC,EAPgB,0BAA0B,0CAA1B,0BAA0B,QAO1C;AAED,SAAgB,+BAA+B,CAC7C,yBAAoD;IAEpD,OAAO,IAAI,CAAC,SAAS,CACnB,gDAAwC,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAC1E,CAAC;AACJ,CAAC;AAED,SAAgB,iCAAiC,CAC/C,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,+CAAuC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACnE,uDAAuD,CACxD,CAAC;AACJ,CAAC"}