"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeltaMessage$ = exports.DeltaMessage$outboundSchema = exports.DeltaMessage$inboundSchema = exports.Content$ = exports.Content$outboundSchema = exports.Content$inboundSchema = void 0;
exports.contentToJSON = contentToJSON;
exports.contentFromJSON = contentFromJSON;
exports.deltaMessageToJSON = deltaMessageToJSON;
exports.deltaMessageFromJSON = deltaMessageFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const contentchunk_js_1 = require("./contentchunk.js");
const toolcall_js_1 = require("./toolcall.js");
/** @internal */
exports.Content$inboundSchema = z.union([z.string(), z.array(contentchunk_js_1.ContentChunk$inboundSchema)]);
/** @internal */
exports.Content$outboundSchema = z.union([z.string(), z.array(contentchunk_js_1.ContentChunk$outboundSchema)]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Content$;
(function (Content$) {
    /** @deprecated use `Content$inboundSchema` instead. */
    Content$.inboundSchema = exports.Content$inboundSchema;
    /** @deprecated use `Content$outboundSchema` instead. */
    Content$.outboundSchema = exports.Content$outboundSchema;
})(Content$ || (exports.Content$ = Content$ = {}));
function contentToJSON(content) {
    return JSON.stringify(exports.Content$outboundSchema.parse(content));
}
function contentFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.Content$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'Content' from JSON`);
}
/** @internal */
exports.DeltaMessage$inboundSchema = z.object({
    role: z.nullable(z.string()).optional(),
    content: z.nullable(z.union([z.string(), z.array(contentchunk_js_1.ContentChunk$inboundSchema)])).optional(),
    tool_calls: z.nullable(z.array(toolcall_js_1.ToolCall$inboundSchema)).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "tool_calls": "toolCalls",
    });
});
/** @internal */
exports.DeltaMessage$outboundSchema = z.object({
    role: z.nullable(z.string()).optional(),
    content: z.nullable(z.union([z.string(), z.array(contentchunk_js_1.ContentChunk$outboundSchema)])).optional(),
    toolCalls: z.nullable(z.array(toolcall_js_1.ToolCall$outboundSchema)).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        toolCalls: "tool_calls",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var DeltaMessage$;
(function (DeltaMessage$) {
    /** @deprecated use `DeltaMessage$inboundSchema` instead. */
    DeltaMessage$.inboundSchema = exports.DeltaMessage$inboundSchema;
    /** @deprecated use `DeltaMessage$outboundSchema` instead. */
    DeltaMessage$.outboundSchema = exports.DeltaMessage$outboundSchema;
})(DeltaMessage$ || (exports.DeltaMessage$ = DeltaMessage$ = {}));
function deltaMessageToJSON(deltaMessage) {
    return JSON.stringify(exports.DeltaMessage$outboundSchema.parse(deltaMessage));
}
function deltaMessageFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.DeltaMessage$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'DeltaMessage' from JSON`);
}
//# sourceMappingURL=deltamessage.js.map