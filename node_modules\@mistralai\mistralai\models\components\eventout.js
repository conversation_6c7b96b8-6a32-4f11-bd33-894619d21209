"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventOut$ = exports.EventOut$outboundSchema = exports.EventOut$inboundSchema = void 0;
exports.eventOutToJSON = eventOutToJSON;
exports.eventOutFromJSON = eventOutFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.EventOut$inboundSchema = z.object({
    name: z.string(),
    data: z.nullable(z.record(z.any())).optional(),
    created_at: z.number().int(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "created_at": "createdAt",
    });
});
/** @internal */
exports.EventOut$outboundSchema = z.object({
    name: z.string(),
    data: z.nullable(z.record(z.any())).optional(),
    createdAt: z.number().int(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        createdAt: "created_at",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var EventOut$;
(function (EventOut$) {
    /** @deprecated use `EventOut$inboundSchema` instead. */
    EventOut$.inboundSchema = exports.EventOut$inboundSchema;
    /** @deprecated use `EventOut$outboundSchema` instead. */
    EventOut$.outboundSchema = exports.EventOut$outboundSchema;
})(EventOut$ || (exports.EventOut$ = EventOut$ = {}));
function eventOutToJSON(eventOut) {
    return JSON.stringify(exports.EventOut$outboundSchema.parse(eventOut));
}
function eventOutFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.EventOut$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'EventOut' from JSON`);
}
//# sourceMappingURL=eventout.js.map