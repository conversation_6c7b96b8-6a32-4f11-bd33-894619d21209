"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseDoneEvent$ = exports.ResponseDoneEvent$outboundSchema = exports.ResponseDoneEvent$inboundSchema = exports.ResponseDoneEventType$ = exports.ResponseDoneEventType$outboundSchema = exports.ResponseDoneEventType$inboundSchema = exports.ResponseDoneEventType = void 0;
exports.responseDoneEventToJSON = responseDoneEventToJSON;
exports.responseDoneEventFromJSON = responseDoneEventFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const conversationusageinfo_js_1 = require("./conversationusageinfo.js");
exports.ResponseDoneEventType = {
    ConversationResponseDone: "conversation.response.done",
};
/** @internal */
exports.ResponseDoneEventType$inboundSchema = z.nativeEnum(exports.ResponseDoneEventType);
/** @internal */
exports.ResponseDoneEventType$outboundSchema = exports.ResponseDoneEventType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ResponseDoneEventType$;
(function (ResponseDoneEventType$) {
    /** @deprecated use `ResponseDoneEventType$inboundSchema` instead. */
    ResponseDoneEventType$.inboundSchema = exports.ResponseDoneEventType$inboundSchema;
    /** @deprecated use `ResponseDoneEventType$outboundSchema` instead. */
    ResponseDoneEventType$.outboundSchema = exports.ResponseDoneEventType$outboundSchema;
})(ResponseDoneEventType$ || (exports.ResponseDoneEventType$ = ResponseDoneEventType$ = {}));
/** @internal */
exports.ResponseDoneEvent$inboundSchema = z.object({
    type: exports.ResponseDoneEventType$inboundSchema.default("conversation.response.done"),
    created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
        .optional(),
    usage: conversationusageinfo_js_1.ConversationUsageInfo$inboundSchema,
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "created_at": "createdAt",
    });
});
/** @internal */
exports.ResponseDoneEvent$outboundSchema = z.object({
    type: exports.ResponseDoneEventType$outboundSchema.default("conversation.response.done"),
    createdAt: z.date().transform(v => v.toISOString()).optional(),
    usage: conversationusageinfo_js_1.ConversationUsageInfo$outboundSchema,
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        createdAt: "created_at",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ResponseDoneEvent$;
(function (ResponseDoneEvent$) {
    /** @deprecated use `ResponseDoneEvent$inboundSchema` instead. */
    ResponseDoneEvent$.inboundSchema = exports.ResponseDoneEvent$inboundSchema;
    /** @deprecated use `ResponseDoneEvent$outboundSchema` instead. */
    ResponseDoneEvent$.outboundSchema = exports.ResponseDoneEvent$outboundSchema;
})(ResponseDoneEvent$ || (exports.ResponseDoneEvent$ = ResponseDoneEvent$ = {}));
function responseDoneEventToJSON(responseDoneEvent) {
    return JSON.stringify(exports.ResponseDoneEvent$outboundSchema.parse(responseDoneEvent));
}
function responseDoneEventFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ResponseDoneEvent$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ResponseDoneEvent' from JSON`);
}
//# sourceMappingURL=responsedoneevent.js.map